{"__meta": {"id": "X5b2f9aed6fca72cfdacedf4ae935af6e", "datetime": "2025-06-16 05:04:36", "utime": 1750050276.015075, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750050274.292049, "end": 1750050276.015117, "duration": 1.7230679988861084, "duration_str": "1.72s", "measures": [{"label": "Booting", "start": 1750050274.292049, "relative_start": 0, "end": **********.788671, "relative_end": **********.788671, "duration": 1.496622085571289, "duration_str": "1.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.788693, "relative_start": 1.4966440200805664, "end": 1750050276.015122, "relative_end": 5.0067901611328125e-06, "duration": 0.22642898559570312, "duration_str": "226ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45157584, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01768, "accumulated_duration_str": "17.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.898004, "duration": 0.01524, "duration_str": "15.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.199}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.960826, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.199, "width_percent": 6.222}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9875162, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.421, "width_percent": 7.579}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV", "_previous": "array:1 [\n  \"url\" => \"http://localhost/settings\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1929868505 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1929868505\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1563771657 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1563771657\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-167519425 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167519425\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-937225358 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwt%7C0%7C1982; XSRF-TOKEN=eyJpdiI6IlpyM3pEOTZPSkxNN2RKYWxjWWJNYXc9PSIsInZhbHVlIjoib0NlS21WNUl4N2hZcjR2ZE02ZGJMQ2JjTnM5R0pNSDY3STRveHZPQ0VKV3IyeERCTys2WWt0QzNtVEtOTXR1VnVBNmpkQ3hMc2g0Y2xUdlBHS2tXeXVOOU9QVmpqbGlQNGRkR1hSem16dUg3eDNQQnRvVWR6aE5CakZZS1R3dWZQOGV6eFQwbFZCZC9SamhXWElVV0Q4T2VmbEJ1YVdZeUVMQVMvb3JXL2Q5a0dVRGlLWFNxK1BPclV4OVI4TkNlb0hLY2JsaWNMSEMzSGZYOUJnRVI0dkVFTzBCUW5jdUowa25GVXB6KzZpREp5TVBQRHJDLzdOZE50czJjVUdYUDlCMEFrcndwbUorNnJsbmg2U2RkUHVQWDRGY3hBOEQzZFRvKzRFUXpzQnpvK21QR2tvVmdLbm9TUmRsSzdEQXdvcFFOQmFzSktZUGV6THFGNiswK3NFZlpMeDZFL00yYkVua1A1SVJpakdpRHNoT283cVhCdHNaL1J4OGNKYUVHcEhaR3hNRDRyamhsM2xkbDI0RlkzT2JBcnRjaU1VdEZPZnFvMUlNMDNZNUlWcUUwUFhpaU0xL1R3NUd5U01Rc281SFZwcy9rOWhZVEV6eitiKytaZGFLRDZMYnR1WXFGYVovZWtLY0w0cjFweFNtb28xaElFdHRlV0cvZUs3SE0iLCJtYWMiOiIxMjEyMzhlYjRlZDgxYjU1NTNmMDhmZjE5ODFjZWVhZDk1YWZiYjBlYmNhZGM3MjQ0YWVlOWU4MmI4YTUzOTYwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkJRdUFjOUZBVDlMTEdGbmNQUHlnSEE9PSIsInZhbHVlIjoiRXJENDRWSUNJMEViRVl6TW9pcmlsTmt6dGNwb082S0xqbWZvTjZUZnpmZ0NtWHVnZlE1MWI1SHI5ZnhLemdRSkhZMmd2UkpnVklxeVlINldMVlBKTUVsd2htQmwvSUtxWWtjMC9uL2w3dGtBYVJUajRhMDNvMW81bzRDZ1NjVUxhMFg4a09vdjBvcEJWUlFpY29yblE5Z1gwR2R1WG0wQytTREYzSVFBU2hEck1vNmpaL2ZsdjFaaW0wR0ZObVppa1F1aHEybkVOWE9ob3kxOHp1T21YRjdQRWdxZlJ1eFVPSS9kdktqK3lVLzRtTkFGK1RJMk96bHhPL3hmcnlRS2xWa3g3Yjd0ek1WdUZxcW12Mm5ZVGJBS0FGV3hhS3FER1Nod0VEL1BUQXJEcERuSW1zR1V5WmR5WjZCMEJVSWxuMERoUXhDbXI1eGgwcWZHVmFVL3R6dkFrOXQvaVREUjdOemFtcW1EeFM3RE1XK1hnZlhPT0EwWFpaMHJyaGZaSW9KVFk1Q2dsVVZxRWg4R3RSdGUrTHlGdnRTYm1TOC93T2FvUHcvYU9xUjJLMFZlbWhOWnlTcytzUExTVWJSZFZqcDV2YnZjbG1pS1FvNkpidG0yVzkxbnFjblE1SUJVYllwbnRMK2dKUWlad2FIUjFGdDJxVzI3b0xROTN1c1IiLCJtYWMiOiJkNWRkMTRiNzk4NmYyZmYzODdkOGJmYWRiYjEwMDQ3NTVlY2VkMDNkYTU0YTk0MDAzN2JjOTRiNzVlOTEwMDVlIiwidGFnIjoiIn0%3D; _clsk=1y3ys69%7C1750050273907%7C5%7C1%7Cq.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-937225358\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-720046633 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dd7SOgI9LQyGBRLGv6JzixDD4MEdY1rjd07Rxzr2</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-720046633\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-966956776 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 05:04:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlMvbjZnZzY3b040aCtlUVNPWWFoenc9PSIsInZhbHVlIjoiTmFtc2JuUEpkWjJwSDBCa1l3bXpZMmlJekJwWEo5bFZhN0RwU2E2RTY2ZEdtU0JyNzRvcjk0emV2UXlwSS9tb0ZKN3NZeFVFUC82amJORjBOSUpGM2ZEVFlNdTlHbXhSYU9ZNzZDSXRPRjZWeXdSVCs1bFBWei9lNHh1U0s0TUlNS0tMb3ZuQmhoQVY5WEdQTlN1VzBKOHZCdlE3MGJobW4yVUR6cWhBTm1kUm45Q1JaWDdMMkRQa0drdkkyWUNRU0lVbWxQa3RkaGg1aFJTVWlRT244MkVGNXd2Z0VDeVNOc04zc3UrblF5azdlZlVadDl3a3FhVFNIYUpxbUtrUncwRzBpVDV6OTY0R2dDTk9Rb2NaUTdaWWhqL3R4VllUMXVsNE1iZElUaHJqV082T0J6YUVyTFdHNVpSTm5mTTlQQ000eXRsWWpWcWE1L3E0OWNTTk1SODRnT0QyK3plbXlveDlzNlAxckJEVkdjQzlEeGlFUmxieTB5Snc3c0pJZUFTKzZhSmZDVlJJZkpRS3MvTUxwZGtxYU9HaXh2aFdFdk9SMlZTaWVZUkZQNU8vcGVpdmNhblZWWDc4ODhQWWxXb1o3eDJEQWRQTzRpZ2Q5bUFqRjZKcDM1MWRnMUJPMWVJUm5ra2RIRFlqY054eXh0TEx3Slp3OE10Y3RYZUEiLCJtYWMiOiJkYjMxZDc1MTY4ZDFkZjNiNWM3ZWVkNzA2YzEwNWEwZmZkZjIxODA1NWEzZjRlNmQyMGNmMmFlYjVhNzE3ZDkzIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlZYV1lKZ0duaWRXQXpaT2kyTm5HbXc9PSIsInZhbHVlIjoiMmNOOW5RaXRhSGRTeWlGNHVQOXBlN0RiamY2TXJhNVYxeU1NZ0x0L3J2VThtUlJVaXJJWVM5cjBSYzNsSmduRXErSHVwUUp4MVZ0TjZSSkszYm5Tdk4vdzZhWVhSeXNNL3JWUUNpYXlSdzhsUE5FUC9vZ1FZUTgvTHpQaUI5VGJ5UkpsTUdpNmorakFlKytNSHVhTDc5RFZwMGpZODN2dlB4d3BqNko4N1BZVEFoeGZYWW9HOFNCc3lWNjRVU2NMNUJ4SHZEMnJuaE9uWEthcnRZenE2bHRVVURrU1ZqcWVKSHRoSjMxdWFJaFBNaklhcGtKOGFJV3NMdjdOcHpwRldMNldkOUphajNrSGREL2Y4dUNtalM2Q3oxMk1DdkFMVUdmKzdTajFpOTUzSWV3ZE5QeTQ3K1ZXNHZidDU3eGJxWTl1NFN6Q2cyKzIwRWNDRy8xTGZsbmJkdFJxOUpFMWdPK2NJUUlYc3B6QlFIaVd6NlI3bVhpOVZ1cHpQRUlzckx4V0l5S1VKQkVqYWJHeU1mVUJxNTZxY0xVOUZLcjNyekM0VFJHSk9pcDdiUmZzazA1U2lMaDJxUVprZlYxZ3RJb0xSTkh4MG9oNzM3TXhwNXZJREZ0NTRHVmZybkRobFR3KzF3dzhsOGROZ1RZREYzL240Ulg1Q1FTbkZPd2oiLCJtYWMiOiJkZDE4NjY3OTNjMzE0NGY0YWQwYTQ3MmI2M2NkM2UzYWI1ODA2OTIwYjM5ZTFkOTYyNGRkNDBhMTBkMDZkMjI3IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlMvbjZnZzY3b040aCtlUVNPWWFoenc9PSIsInZhbHVlIjoiTmFtc2JuUEpkWjJwSDBCa1l3bXpZMmlJekJwWEo5bFZhN0RwU2E2RTY2ZEdtU0JyNzRvcjk0emV2UXlwSS9tb0ZKN3NZeFVFUC82amJORjBOSUpGM2ZEVFlNdTlHbXhSYU9ZNzZDSXRPRjZWeXdSVCs1bFBWei9lNHh1U0s0TUlNS0tMb3ZuQmhoQVY5WEdQTlN1VzBKOHZCdlE3MGJobW4yVUR6cWhBTm1kUm45Q1JaWDdMMkRQa0drdkkyWUNRU0lVbWxQa3RkaGg1aFJTVWlRT244MkVGNXd2Z0VDeVNOc04zc3UrblF5azdlZlVadDl3a3FhVFNIYUpxbUtrUncwRzBpVDV6OTY0R2dDTk9Rb2NaUTdaWWhqL3R4VllUMXVsNE1iZElUaHJqV082T0J6YUVyTFdHNVpSTm5mTTlQQ000eXRsWWpWcWE1L3E0OWNTTk1SODRnT0QyK3plbXlveDlzNlAxckJEVkdjQzlEeGlFUmxieTB5Snc3c0pJZUFTKzZhSmZDVlJJZkpRS3MvTUxwZGtxYU9HaXh2aFdFdk9SMlZTaWVZUkZQNU8vcGVpdmNhblZWWDc4ODhQWWxXb1o3eDJEQWRQTzRpZ2Q5bUFqRjZKcDM1MWRnMUJPMWVJUm5ra2RIRFlqY054eXh0TEx3Slp3OE10Y3RYZUEiLCJtYWMiOiJkYjMxZDc1MTY4ZDFkZjNiNWM3ZWVkNzA2YzEwNWEwZmZkZjIxODA1NWEzZjRlNmQyMGNmMmFlYjVhNzE3ZDkzIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlZYV1lKZ0duaWRXQXpaT2kyTm5HbXc9PSIsInZhbHVlIjoiMmNOOW5RaXRhSGRTeWlGNHVQOXBlN0RiamY2TXJhNVYxeU1NZ0x0L3J2VThtUlJVaXJJWVM5cjBSYzNsSmduRXErSHVwUUp4MVZ0TjZSSkszYm5Tdk4vdzZhWVhSeXNNL3JWUUNpYXlSdzhsUE5FUC9vZ1FZUTgvTHpQaUI5VGJ5UkpsTUdpNmorakFlKytNSHVhTDc5RFZwMGpZODN2dlB4d3BqNko4N1BZVEFoeGZYWW9HOFNCc3lWNjRVU2NMNUJ4SHZEMnJuaE9uWEthcnRZenE2bHRVVURrU1ZqcWVKSHRoSjMxdWFJaFBNaklhcGtKOGFJV3NMdjdOcHpwRldMNldkOUphajNrSGREL2Y4dUNtalM2Q3oxMk1DdkFMVUdmKzdTajFpOTUzSWV3ZE5QeTQ3K1ZXNHZidDU3eGJxWTl1NFN6Q2cyKzIwRWNDRy8xTGZsbmJkdFJxOUpFMWdPK2NJUUlYc3B6QlFIaVd6NlI3bVhpOVZ1cHpQRUlzckx4V0l5S1VKQkVqYWJHeU1mVUJxNTZxY0xVOUZLcjNyekM0VFJHSk9pcDdiUmZzazA1U2lMaDJxUVprZlYxZ3RJb0xSTkh4MG9oNzM3TXhwNXZJREZ0NTRHVmZybkRobFR3KzF3dzhsOGROZ1RZREYzL240Ulg1Q1FTbkZPd2oiLCJtYWMiOiJkZDE4NjY3OTNjMzE0NGY0YWQwYTQ3MmI2M2NkM2UzYWI1ODA2OTIwYjM5ZTFkOTYyNGRkNDBhMTBkMDZkMjI3IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966956776\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-591188445 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591188445\", {\"maxDepth\":0})</script>\n"}}