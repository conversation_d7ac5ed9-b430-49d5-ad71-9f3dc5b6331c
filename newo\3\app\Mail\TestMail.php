<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class TestMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('email.test_mail')
                    ->subject('اختبار إعدادات البريد الإلكتروني - Test Email Configuration')
                    ->with([
                        'test_time' => now()->format('Y-m-d H:i:s'),
                        'app_name' => config('app.name', 'Laravel App')
                    ]);
    }
}
