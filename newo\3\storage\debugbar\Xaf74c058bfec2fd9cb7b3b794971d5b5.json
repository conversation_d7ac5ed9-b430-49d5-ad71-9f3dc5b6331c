{"__meta": {"id": "Xaf74c058bfec2fd9cb7b3b794971d5b5", "datetime": "2025-06-16 04:58:18", "utime": 1750049898.679052, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750049894.325894, "end": 1750049898.679094, "duration": 4.3531999588012695, "duration_str": "4.35s", "measures": [{"label": "Booting", "start": 1750049894.325894, "relative_start": 0, "end": **********.515872, "relative_end": **********.515872, "duration": 2.1899778842926025, "duration_str": "2.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.515933, "relative_start": 2.1900389194488525, "end": 1750049898.679098, "relative_end": 3.814697265625e-06, "duration": 2.1631648540496826, "duration_str": "2.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43492320, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 1.****************, "accumulated_duration_str": "1.81s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.827921, "duration": 1.****************, "duration_str": "1.81s", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rEMkGLQOKgsJHVrqftO2h3y7pwXm6bpmJHmiTPKg", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1279609316 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1279609316\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1368661521 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1368661521\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2112953612 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"137 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112953612\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-593288156 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-593288156\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1687676436 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 04:58:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkV3dW03ZnJNSUJLN2RlTVk4UkJheFE9PSIsInZhbHVlIjoiT0k3a1c1eDUxWDN1S2JVVUgxcHNUcGxPcWRlSVQ2UzAxWHZZU2pCK1pKOVd2eFM2dmhscjRyb3k2ZE84aXdhQWlCUGx5Q2wwZWhrMmpxNFpIVTIzWVVhOWVLbzFWYytPeXl2cC8weGxDRXFTWmMzazFWQkQ4a0VzdUpuQ0g3MFdsdnhMbGR0M0FvWklFR1lBdjFSdHJ4NWJqcGE3dnQ4Y3RiOHZrMEZMQnBhQTlXR3h0c1hQcXY5cExubXpzb3BXUjVocUJQTmhoZ0l0SUVEMXFzREpZMDdMa3pid3h0SnlYTnAvbWJFL2hITGEzMVViU0ZQYXQ4cDJ5TWpIOW9ES3F5V3ZHSFN6SWM0eHFOUWZ1djBGUjRCd2YybWIyZHhuNDgzU2wwOE9PSnlGRHBpWVFweGNGdkxqazFDcE1zM1BaUkF3a0JUU1BOalVHbThISW95ZDFUc0hhRmhyNWdzSVZaQVJOU2xGenRDN1JidVJIcnZwYVVIZzVobWlaRVIvY1FwSUIzYzNTS3QzNEtRdUZXYTlrNWhVbkYxeHZyQ3FZQVNzUmkzYmxsWFd0MVc2L3NvOVNub1UxbHBKTGVFSXRGK1FSbkltd095em15S1FabTNGL3NXNytjZ0NzVGxwbVlSQWNpY05CYlZOak5uZzQ4eURUSWV3bStMU21iMVMiLCJtYWMiOiJiMjViYjNlZGZlMzk2ZDI5MmU3ZTBlM2QwNjVmOGNhMjA3MDVhNjFlZTNmYzg2NTM2NjU0YzA0MGVkODFlNzhmIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 06:58:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRmbVQ3TnIyeWtzZTgwSzhRcStqZ1E9PSIsInZhbHVlIjoiblIyY2NLeC9TMDZJUDlSeEhRWmpWS0ZEUDZkYUtRblBnZFh2MjcxZDRhVE52V05iZUFNYWFuUzNVNVl6WjJISE04RVNQaUUxcGJRVEU3V3B0VWpjQ2NDMUlRMnpmK2E5Qjd1cXFiZzdBeE9PZU1RSGVncVZaRzBvL3ZvWlBDQjQzc1dNMW5mejRobzNpV1FMQjJQUnRramNveEdYakc4MTY4bUlkanVLN1pwL3dKMjdwWXJjaXQ2TWtaeUhadVZJNnhEWHBBeXdmWkFYblFxK3h3VngxbFBuUTl6VWkzUjdwOUkraElvZWRXMTVHd0RtcnhjRmp5TGF6NUhrM2ZRYVUyWXNsRFFEWjZISExWMHlJT0daQkRhSDNPZnkxTzQ1MlpoTDhEb1QxVi9iNFIwdWp4Rldpd3hHZWd3RnY4cFo1OWF2cnE2YkZTOFhsVncrUkdabFhnWFA2ZzlNazJCUS9uTUtybWNpZzFQZHkrZnNVT04wbkRueVhPMWRTb3RhRDNEeFRnYnY4QnREYzJLVURQZ2VXVHFzd2hWZkJXOGlkYmFKcFN5TnB3c1BrVnc5NmhvVXJhem96RkZMTVpKNy9pNEp2Zy9YMEJrL21UaDFsKzJJL2hld1d1TUVtdVdocUQ2WVhvbTBZbW1mTlVSWU9SdEIzeWxDWFFiN3BMd2siLCJtYWMiOiI0NmY4NzcxZWI4ODMxZjFlMDlhZTVhMTE3NDg3ODM2ZDdhYWU3M2E4YmQ2MmI2OTIzY2JkNWI2ZjczOGI4Y2I0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 06:58:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkV3dW03ZnJNSUJLN2RlTVk4UkJheFE9PSIsInZhbHVlIjoiT0k3a1c1eDUxWDN1S2JVVUgxcHNUcGxPcWRlSVQ2UzAxWHZZU2pCK1pKOVd2eFM2dmhscjRyb3k2ZE84aXdhQWlCUGx5Q2wwZWhrMmpxNFpIVTIzWVVhOWVLbzFWYytPeXl2cC8weGxDRXFTWmMzazFWQkQ4a0VzdUpuQ0g3MFdsdnhMbGR0M0FvWklFR1lBdjFSdHJ4NWJqcGE3dnQ4Y3RiOHZrMEZMQnBhQTlXR3h0c1hQcXY5cExubXpzb3BXUjVocUJQTmhoZ0l0SUVEMXFzREpZMDdMa3pid3h0SnlYTnAvbWJFL2hITGEzMVViU0ZQYXQ4cDJ5TWpIOW9ES3F5V3ZHSFN6SWM0eHFOUWZ1djBGUjRCd2YybWIyZHhuNDgzU2wwOE9PSnlGRHBpWVFweGNGdkxqazFDcE1zM1BaUkF3a0JUU1BOalVHbThISW95ZDFUc0hhRmhyNWdzSVZaQVJOU2xGenRDN1JidVJIcnZwYVVIZzVobWlaRVIvY1FwSUIzYzNTS3QzNEtRdUZXYTlrNWhVbkYxeHZyQ3FZQVNzUmkzYmxsWFd0MVc2L3NvOVNub1UxbHBKTGVFSXRGK1FSbkltd095em15S1FabTNGL3NXNytjZ0NzVGxwbVlSQWNpY05CYlZOak5uZzQ4eURUSWV3bStMU21iMVMiLCJtYWMiOiJiMjViYjNlZGZlMzk2ZDI5MmU3ZTBlM2QwNjVmOGNhMjA3MDVhNjFlZTNmYzg2NTM2NjU0YzA0MGVkODFlNzhmIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 06:58:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRmbVQ3TnIyeWtzZTgwSzhRcStqZ1E9PSIsInZhbHVlIjoiblIyY2NLeC9TMDZJUDlSeEhRWmpWS0ZEUDZkYUtRblBnZFh2MjcxZDRhVE52V05iZUFNYWFuUzNVNVl6WjJISE04RVNQaUUxcGJRVEU3V3B0VWpjQ2NDMUlRMnpmK2E5Qjd1cXFiZzdBeE9PZU1RSGVncVZaRzBvL3ZvWlBDQjQzc1dNMW5mejRobzNpV1FMQjJQUnRramNveEdYakc4MTY4bUlkanVLN1pwL3dKMjdwWXJjaXQ2TWtaeUhadVZJNnhEWHBBeXdmWkFYblFxK3h3VngxbFBuUTl6VWkzUjdwOUkraElvZWRXMTVHd0RtcnhjRmp5TGF6NUhrM2ZRYVUyWXNsRFFEWjZISExWMHlJT0daQkRhSDNPZnkxTzQ1MlpoTDhEb1QxVi9iNFIwdWp4Rldpd3hHZWd3RnY4cFo1OWF2cnE2YkZTOFhsVncrUkdabFhnWFA2ZzlNazJCUS9uTUtybWNpZzFQZHkrZnNVT04wbkRueVhPMWRTb3RhRDNEeFRnYnY4QnREYzJLVURQZ2VXVHFzd2hWZkJXOGlkYmFKcFN5TnB3c1BrVnc5NmhvVXJhem96RkZMTVpKNy9pNEp2Zy9YMEJrL21UaDFsKzJJL2hld1d1TUVtdVdocUQ2WVhvbTBZbW1mTlVSWU9SdEIzeWxDWFFiN3BMd2siLCJtYWMiOiI0NmY4NzcxZWI4ODMxZjFlMDlhZTVhMTE3NDg3ODM2ZDdhYWU3M2E4YmQ2MmI2OTIzY2JkNWI2ZjczOGI4Y2I0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 06:58:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1687676436\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-990152952 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEMkGLQOKgsJHVrqftO2h3y7pwXm6bpmJHmiTPKg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-990152952\", {\"maxDepth\":0})</script>\n"}}