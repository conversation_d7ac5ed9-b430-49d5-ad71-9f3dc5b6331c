{"__meta": {"id": "Xd07edd4737de2a5ad471c4f83c42ccd3", "datetime": "2025-06-16 05:04:03", "utime": **********.639437, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750050241.802948, "end": **********.639481, "duration": 1.8365330696105957, "duration_str": "1.84s", "measures": [{"label": "Booting", "start": 1750050241.802948, "relative_start": 0, "end": **********.332823, "relative_end": **********.332823, "duration": 1.5298750400543213, "duration_str": "1.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.332846, "relative_start": 1.529897928237915, "end": **********.639485, "relative_end": 3.814697265625e-06, "duration": 0.3066389560699463, "duration_str": "307ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45206824, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02923, "accumulated_duration_str": "29.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.433257, "duration": 0.02247, "duration_str": "22.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 76.873}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4968722, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 76.873, "width_percent": 6.432}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.576528, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 83.305, "width_percent": 9.408}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.604733, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.713, "width_percent": 7.287}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-367591329 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-367591329\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1472287345 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1472287345\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1968295502 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968295502\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1715946405 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwt%7C0%7C1982; _clsk=1y3ys69%7C1750050167372%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImppTHd5Um01WS9zdzB0alNiZlNyR3c9PSIsInZhbHVlIjoiMU9xWHRPY2xYQzlZc2xmUVVKM2FzcyswNHFzcHRnVlBJa3hNekpGV1J6eHhjVFJFeUdIOFRMV1E3UGl1cjBHdC91TzRyenBUeFFGOG5NYXl6MHFRaWd1K1g1NENmMEg3cFlrTGxaUUhDc2xqNTNrZ2E0ckNpTTdDRG82cGpZblF2WnZ1RVFvMG0vU1I5ei9MdTUzMkNkN256SmR5VlVVOWRIbS9ncmFlTlh0YkJTK3hsSkV0dnhIdkVxemRwQnVhOTR1OGsyeFdqUXhqc3ZNQjJPZ3RJd3I1UkxkL2FjUGt3d2hOSWF1NTdQaU1JRmRGbVJQQzBkenJKcjNnM0xjS2pZVGFvT3pJdi9KdFF2cnErVlQ3RHNPUlRCVSsxaW5adzlzdG9uaEdwWXFvdndGZ3NueW4yaEdPaEJGa0RGRTZidFlFRFNQU241TG13V2lhWjUwelAzcUxsRUpFa2QybDRFdWg0TkZtSXBZL3hPUEdBZGVFMGtHZm5xbU95REVlODRUbFZlaFhHZXdsYlh6bnZXa2lOV3VZVHc3WmVnVlMxc2VCRG52MnJqUjVLdmFCV1ZxMlZ5ZXMvdU1uR3V0RTZvR2hkckVmQ1BtcERsN1M3Q1llMWtGb014MHl6Qm4yc0dGbzJ6WjZIU3hvZWU5UjI5Z1pLM0FxS1dHOGRVdEsiLCJtYWMiOiJhMTExNTU3Y2ZhYzk4MzE4OTdlNGJlMTEwOTA0MzgxZDIyM2EyZjg3OGMzYzc2MzIwNjJkMTc5MTNlYjU5ODZiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJpYVlHWk1ueUJzSlFSWGMzTzJzYmc9PSIsInZhbHVlIjoiS3FMcGo1bG1lSE5ETnd4WFpzaFl6NTJnVm0vMGFMN0s1TzltcThtZCtKNnlCUEpjZFoxNDdUQS9WM1ZQejY0ZzRLbFZpOHlDZnJ6UWVqZ0xmK2d0QmxWS2tNWWZndW0rU29FZWZXejhZbCsxdUdrZFpMeDVldGloanJBcUZDS0tKc2Izd096NW5EUFh4SXhoZWFJTmE3QXlGRG9CNHdjSTRWZlVvVDhvK1QrbFNCY0JPQ25UYVZ6SmM1MitMZFVBTFgvSG1HaWpZNzBXazU3NGFQV01Hbi9hK3hzdFBJNXdSODFQSXhzQVhONkJudXltMXc5Mkw4LzNzTVVqWUVYQ1NNcFZzbjNIWlNJOWlYYStEdGJmT0NNRE1WZm1WZ3Y5N3JJYkp0akJjcmxyNk11UUs2MUdEUkpyS1JQMi9HRXFLZHIwczIwQjFDS0hIL3ZGL0JEWFVuS1oySWlxREtORTVlZHh1UEdqN2pCS04wU1RWZHppL3RlU1UzMXRWMW1lMkljcXBVM3U5WFVvNjJkWWxweUx5Z1VQSC9JemFFUEFDREJOaFd6UEhEcjkwYWxQbklkUlRPTTByRTdHVENVNHVKSGYzRFNqc1BwcE9HeDdLbVFwOWRoNWhrSmIvOFYvdDBSdVNvZEY2QjlxNEVLWGVrNkVYZnd4OVk4dnVXMjIiLCJtYWMiOiJlZGYxZTk2YThkZmVhZjFhZjVlMzg0ZWIxMjdkNjI5N2E3MDgwMjI2MzY2ZjAzY2NlM2FmYTExOWJlN2Q3MDAxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1715946405\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-534677052 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">F52gGvLLY2TuuySQOODF6RGytIRL64hrZr70fxyL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-534677052\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-648221101 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 05:04:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlJOVN4M0FhNzFreWdDRHdWMkhhbnc9PSIsInZhbHVlIjoiQTZEalQ1ajUwVUZrQTJJOTlVVm9MeUpZY01kQ2lrM2llTHNQWUdZOElnK1E0MHdTT2lIMStKOGkwMytPT3IxY1lXNFJFU3dZMVRIRkRmb2RsRXBSY3pnaEo5MnJCMXJ6N1pRWlEyNHIrc2h1Tm5zQXVtV1kyOXNjemo0U1Q2RUJHNzdZOFhJQzBpV202TXlReW1Ja05ITTRQV1FOOGpJTytXZWprbU5KRERVQVZxNllRRWNNaUNQTnEvWmRpR0NNTEo2VFlqV1J3UnlGcEpNL2RselZqdnVqaUtKOXVXVDFzWTducVU3R3dJUHhIVENHOEViN09iOG1WZEEyalk5Y2RXTEkwRFlPdHNVYVpld01vNnFQSTIvNUVrTUQ3U2R4TzRqQTJaNUFPNjB4NzJvWUhNRGxqRVAyVFpJQ1NsSWg0cjhlY21vZDZLRWhJS0dQOXV5KzNLT2tEVHVlK0dKdXhBKzc4SmtSVzlGZGRYK3lkVERkU202ejR2TENDVHBLZytZYzdIaWNXdFFaa0NDTHhJV3RHSEhldjZUV28xV0hKUkpOT2ljdXJTck9EVzFUK09uUGU3YXJPNDlacVdlZmxjZU5ldjdtNWVPZ2Vra25PbWdOd2hkNHZ1UUlGZC8yckI4WTZQWnova2ZjY1ovZ3NmMDVzVFNmWnJ0eDREZDQiLCJtYWMiOiJiMjk1NzQ5NWM2YWNiZTQwYjg4N2MxY2UwYTAwN2QwYWRlODYxMjk2NThhOGYzZDA1NTI1ZDg0YmU1YWFkZDY0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlM0VWRRUGpDRm4yVU5PeHluaWFlRUE9PSIsInZhbHVlIjoiV29LTWhsZ1dyZUE2STA5d2o5bkpocEJGTmxrQmVoQWxxb2pkai9wbTE0LzJzU1hYU3NBZnV4TlQ1TVF1TEhuK0IxNjhCOHFYazBKaVJnZUs0MWRPYU90ZGVsWjQzQTRiNHpLdzl2MWltZTNZd1RpdUhPeTVhajdmMmdxSXdFUFoxTnB6Zk55QldWcWtxSVZrZlBjS0RoUmZGYUpjL2FTT3I5NXNqb2JNeFI1UE96bzNEYm15ZGJyaENpQWxQS1M1cHo0a2U3TTFTSGZGUHMvQkhOS0I4L0kySFd3K3ZlL21tWFlCdy9TNjE4dmdXUy9rc0dCdmdqeTl1ZDY5b2RmRFZraWU5b0JOejVhbFU3U3ZLd29ST3paZFNGOFpvd0lxNEQ3L0pMWHNtMGt1aG84WGYzY2FYWThiS1BFNUl4TjNlVUYwdlFhRGJtd0UxUU5hb28xdTBWYlBJUlQ1WURlUmoyQmVTK1habFpXY21ZUko5Q3ZwS1BsRURHOXNMSDRzYm04VFZWVUEzSGJtTFJaVFhGMnhhMWFjS29zcXAxRm5jVUUyNTlMMGRSY0FxaHRwYjhGeExEd1BiUCtiQlhiMCt0aFVYVjBabmw3ZzRIYVlFaGFSbkpDZUxCRVVuYUwrRDk1amNnOEdhOWxJcnc5Z2hmbVJINmkySzBGYUpJRmYiLCJtYWMiOiI1YTczMzE4ZDk3Zjg0MzU3MDRkNzg1ZWFlM2MxMmYyMTNlZGQ5ZGE2OGUwMjZkOGEzYTZkYThlZDE2ZjJmZGQ4IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlJOVN4M0FhNzFreWdDRHdWMkhhbnc9PSIsInZhbHVlIjoiQTZEalQ1ajUwVUZrQTJJOTlVVm9MeUpZY01kQ2lrM2llTHNQWUdZOElnK1E0MHdTT2lIMStKOGkwMytPT3IxY1lXNFJFU3dZMVRIRkRmb2RsRXBSY3pnaEo5MnJCMXJ6N1pRWlEyNHIrc2h1Tm5zQXVtV1kyOXNjemo0U1Q2RUJHNzdZOFhJQzBpV202TXlReW1Ja05ITTRQV1FOOGpJTytXZWprbU5KRERVQVZxNllRRWNNaUNQTnEvWmRpR0NNTEo2VFlqV1J3UnlGcEpNL2RselZqdnVqaUtKOXVXVDFzWTducVU3R3dJUHhIVENHOEViN09iOG1WZEEyalk5Y2RXTEkwRFlPdHNVYVpld01vNnFQSTIvNUVrTUQ3U2R4TzRqQTJaNUFPNjB4NzJvWUhNRGxqRVAyVFpJQ1NsSWg0cjhlY21vZDZLRWhJS0dQOXV5KzNLT2tEVHVlK0dKdXhBKzc4SmtSVzlGZGRYK3lkVERkU202ejR2TENDVHBLZytZYzdIaWNXdFFaa0NDTHhJV3RHSEhldjZUV28xV0hKUkpOT2ljdXJTck9EVzFUK09uUGU3YXJPNDlacVdlZmxjZU5ldjdtNWVPZ2Vra25PbWdOd2hkNHZ1UUlGZC8yckI4WTZQWnova2ZjY1ovZ3NmMDVzVFNmWnJ0eDREZDQiLCJtYWMiOiJiMjk1NzQ5NWM2YWNiZTQwYjg4N2MxY2UwYTAwN2QwYWRlODYxMjk2NThhOGYzZDA1NTI1ZDg0YmU1YWFkZDY0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlM0VWRRUGpDRm4yVU5PeHluaWFlRUE9PSIsInZhbHVlIjoiV29LTWhsZ1dyZUE2STA5d2o5bkpocEJGTmxrQmVoQWxxb2pkai9wbTE0LzJzU1hYU3NBZnV4TlQ1TVF1TEhuK0IxNjhCOHFYazBKaVJnZUs0MWRPYU90ZGVsWjQzQTRiNHpLdzl2MWltZTNZd1RpdUhPeTVhajdmMmdxSXdFUFoxTnB6Zk55QldWcWtxSVZrZlBjS0RoUmZGYUpjL2FTT3I5NXNqb2JNeFI1UE96bzNEYm15ZGJyaENpQWxQS1M1cHo0a2U3TTFTSGZGUHMvQkhOS0I4L0kySFd3K3ZlL21tWFlCdy9TNjE4dmdXUy9rc0dCdmdqeTl1ZDY5b2RmRFZraWU5b0JOejVhbFU3U3ZLd29ST3paZFNGOFpvd0lxNEQ3L0pMWHNtMGt1aG84WGYzY2FYWThiS1BFNUl4TjNlVUYwdlFhRGJtd0UxUU5hb28xdTBWYlBJUlQ1WURlUmoyQmVTK1habFpXY21ZUko5Q3ZwS1BsRURHOXNMSDRzYm04VFZWVUEzSGJtTFJaVFhGMnhhMWFjS29zcXAxRm5jVUUyNTlMMGRSY0FxaHRwYjhGeExEd1BiUCtiQlhiMCt0aFVYVjBabmw3ZzRIYVlFaGFSbkpDZUxCRVVuYUwrRDk1amNnOEdhOWxJcnc5Z2hmbVJINmkySzBGYUpJRmYiLCJtYWMiOiI1YTczMzE4ZDk3Zjg0MzU3MDRkNzg1ZWFlM2MxMmYyMTNlZGQ5ZGE2OGUwMjZkOGEzYTZkYThlZDE2ZjJmZGQ4IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-648221101\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1433888230 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1433888230\", {\"maxDepth\":0})</script>\n"}}