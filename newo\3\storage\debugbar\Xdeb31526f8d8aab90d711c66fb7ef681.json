{"__meta": {"id": "Xdeb31526f8d8aab90d711c66fb7ef681", "datetime": "2025-06-16 05:04:20", "utime": **********.597924, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750050258.878453, "end": **********.597963, "duration": 1.7195100784301758, "duration_str": "1.72s", "measures": [{"label": "Booting", "start": 1750050258.878453, "relative_start": 0, "end": **********.391963, "relative_end": **********.391963, "duration": 1.51350998878479, "duration_str": "1.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.391994, "relative_start": 1.5135409832000732, "end": **********.597967, "relative_end": 3.814697265625e-06, "duration": 0.20597290992736816, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45157640, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02654, "accumulated_duration_str": "26.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.495726, "duration": 0.023620000000000002, "duration_str": "23.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.998}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.550483, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.998, "width_percent": 4.823}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5711548, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.821, "width_percent": 6.179}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-452002818 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwt%7C0%7C1982; _clsk=1y3ys69%7C1750050242430%7C3%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilkyd2FSN1RSRUVDRlJSUzE4enJVYVE9PSIsInZhbHVlIjoid1NPaDl5bWpPYzA0VTY4eWVkTm5Da2gyQjNpNE9sL3dEdzhXRzQzaS91eWx3MThGM1NHYW5FdjI5Y1FoZEVsdktWL1F2RnQwMXlYR2xuM2JNd1ZCaWxsODlYZ0cvU1V3NnkxWGg0akthQzRFcGFwaWNadTB4bytsOFQvSHBJNExlZjVjMGQvSWlDbFVBU2hWcm9iSUpvVVZUODdZT0E5YUVCUWZTMDg1Z1RhMllIUG1XN0l5aktibmhRc1YxZzFra2RQS1drVm8yZkpSWFdEYWNvdnBRcVVINU1QTk5EV2I3bzhHTVZwRFVMSnFybXA0clJGWFU4S0c2R3JhVWZ6c01acythRkZQakkzNEdnTmJwcWpwYlVlUWVtQXpiVElrZXdYdENqUDl5Z0xNQlNTKzJ0YUJDR1BFRG9sV1dYR0IxQmllOWR6YUxzanNXdy9YK2lFV3o4aytHNWR6YUZNUGd0UytOUVhqN1VXNXlmZE94YmRhdmFQNXhKUTJ0dzM1b3pEYVd6NkJtQ0hkVFMrVmtYVFFXWGZuQjlxcUZjZGM5Y0VsVHlGVWdOWFlpT3g3Ti9SclkrYXJZaElrS21tS2g0ZHhFeU1CV244MS8rQ1VMeUY1cWtyYUNXU2p2RWlsR2IvN0ptR0tNSXRDejNyblBiMVdSVE1BclQ4dUtnRTgiLCJtYWMiOiJlOGQ5ZTVmOGYxZDk1OGVmMjA3OTY4YjMzY2E1MjVjODJlZTJlNzczMjg4ZmU3MjQ0NWExZWI2ZDcxNDZiMWM0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ii8yY1g4R1RBTG1xblhPMnhwSW5ZQ3c9PSIsInZhbHVlIjoiL2dobVlLUUlHbFFFQVFnaTh4eHpJQ1lESXd1VHdWdVpxRVNHQmtzTm9DY2VJZ2NwSEI5eHhYd3l1dDY4angwOVJmRWpQZnpQckl1MmlvM2FSaitpOEk0WDdHQXBnR0VqYXJKQ1lENnNuZXBrc2ZzelpUbjFWUEd6STJoZDYybGVtRzdYbWhmUWUwQWZjSVpvOEdwamthTG1GNmpwb0orSnd3RWcxdUdJTEhHUi9WMDVGVFU4b241M25HREZmSFZXNmVhS1JRS043SER2VTZ3OC94bUN3UkpBRm0wV0szOHVlSW1pU0xMRGNrTlJyYk5HYlFCSW83SWs3NW45eHVyRUtDRXMwa0d1enBza3oybUI4amU2TlJqaHdyMzFpT1pEZ2lQdFZTUFFlaEdxZks2S2lVd1VQS3JjR0M3eFZnU3BJM0Zkai9nZ3JhaStCWXFXM0lQVENlZlkrUGZRRVA4cHluU1RQdW1POXVBQnVWZGJmVHg5bGxQeFpYK3dLWThSbHBuWVhmQmJqV2RqRHpjQjgwalZBeDl1dkhJWE5KVXNEZ3RWR0RWUERTemdBMEZLNHJOWVN4SGtXeTRPcGJnWTZzcEVhL1JZaG80bWNGbGhqcXRENTN5Tk82R2tObW9kamswaVFhSFZGYXg3QnMzdHliSXd4ckJzckgxaHBKYm8iLCJtYWMiOiI5NGY0NDEwMjM3N2QzYmQxYjQyMDljNjc3N2JmZDY5OTM0MTExYTY2ZDU5NzI0YmQxZDA4YjhjNjcyNDQwMzhlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452002818\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1392526811 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dd7SOgI9LQyGBRLGv6JzixDD4MEdY1rjd07Rxzr2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392526811\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1475036872 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 05:04:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ino0TnVFWGlnUTBhVmtHUXBGb0JVZGc9PSIsInZhbHVlIjoiT2dDWXEwN25VbStxVHVsbkNnZkRpTFpaT3ZIbkVSb0U1aHExQlQxMjBCektHb00ybm15QVFkc2lrSjhkYUlEQXhvMVpkc1lPZ0U2bkRUTUJPM1RmU2hCTUZ1N25tNTRDNVU2cU51ZkpwdTBsV2puV2ZXTHF1YkVmZnR5Z0Rqd3NQQS85VXl4dU9Yb2ZrY0tmS1l4Tmo1aHhpQUlhOG1lMDIrK1crMHlaOEVROUNRQktYNnRpL1ZXNC9qR0cyMUFXRXdRbE5pOUdwZEVxeUhFNnFnUk0rMjJobmYrVkJ1eU83Nkcvd3pTSGhaTFZSOElQZUZ3emxTNjVybTdRdnkyZXpPREJPWFg2SG8yZGdOeDlDb2xtYWJIalBvUEJQbUR1ZlFEaHJwKzJ3QmdTTnNES0hPSVlTNHhxTytvTGFjUUNCWDBDNWc5L2NsU1NLQ1NUenVPOGhPV09kK2NqQkNVYkFOQkk0WjhiejZhMjM5Z3M4emI5YXFFNmg5QjdvZGdVeUp3dzBrY2JMNWFKdWxKTkFRbkxSTkRwMVlDaWprNFgzazR2ZUhucldLTXU5TXY0NkUwUXIvRlltVjVJQWttT1pOdDZIdnBYMFpjaFh2aEM4c1l5UVlPcVJHUGsxUHhNMTFPSWNONmdZL1NDSWRuVzlpQkNaQVFJVEtZNTh3djciLCJtYWMiOiJhZDY2ZmU2ZWQ4ZDRkODgyMTNlM2I2OTljYWE1YjI3MDJiNmY1ZGZjNTZlY2YwOTdkYTViY2QzZWE1NGM3NjNjIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjVGU3lhandtVXF3QkhPZVN4bjN4eFE9PSIsInZhbHVlIjoibWlqMTJUVDVIR25OMmFWTlMxZlpXaFRoTm4xSUZWUXN5UjhzL0Q2cG1RMWVOdUlQYzluOUV1TzBPKzlKcnhURjBTbzVDUGZPZFBHZStRL041cWlkdVE5NnllYkRXMkF2Um1yS0ttR2w2cjB1SVF3NDF0cWJMUnQwN01jdTJOQ0NmbndNRzQvWEZxek1sWExNQ3V1Qk5HS0J6MGFXbHo4bHA4cUZ6eUh3K1NjU24xTlF1RXdIUUxsa0xiaDJDcVFLUElJNmpYYXQxVmVJYy9VQldvUXFIeWhtUFp3RGlMNVIzcjlqSWVma2Q3UWZNUi84bGNMQnR2bGllRTdYWVJQdkxvblNXam53SjhocVV5dU5Pem0zM2lYZVRZU0doMGp4QnlxcmczRWZ5azhVdkdqZFFJWjJCd0o0QzM1NFdhV2pvQldRdXpPWUhJRmJEYmx5dFROZ0oybStveEN5QnpyYmNJa1VjUXRLd1FFSnhiQnR3YlpRSUlvU1MvR0EzZVR2NS9uSUlNL3pnbHUzRXVqOVI2d0t3aWRWNkZpNXBUb3BZYlp6SDNwbkJmRmpsYjFNaGZtRGp0eCs5MVFwY3NyVTY5Qml6MzdnZ2hNcjNyTTllbDgxR2lUQzBGUDRHYklRNDVyK0c4WXAvVUh5a293RmNCeXJVVUI4ZGRyS0xVa0MiLCJtYWMiOiI2ZjRmMzMxZjFlOTQxMTA1ODlkMWQ4ODBlM2YyOWEzZWFkYWZlMjJhYzM2ZGQ5NzAwNzliY2NiMjFiMDliNmZlIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ino0TnVFWGlnUTBhVmtHUXBGb0JVZGc9PSIsInZhbHVlIjoiT2dDWXEwN25VbStxVHVsbkNnZkRpTFpaT3ZIbkVSb0U1aHExQlQxMjBCektHb00ybm15QVFkc2lrSjhkYUlEQXhvMVpkc1lPZ0U2bkRUTUJPM1RmU2hCTUZ1N25tNTRDNVU2cU51ZkpwdTBsV2puV2ZXTHF1YkVmZnR5Z0Rqd3NQQS85VXl4dU9Yb2ZrY0tmS1l4Tmo1aHhpQUlhOG1lMDIrK1crMHlaOEVROUNRQktYNnRpL1ZXNC9qR0cyMUFXRXdRbE5pOUdwZEVxeUhFNnFnUk0rMjJobmYrVkJ1eU83Nkcvd3pTSGhaTFZSOElQZUZ3emxTNjVybTdRdnkyZXpPREJPWFg2SG8yZGdOeDlDb2xtYWJIalBvUEJQbUR1ZlFEaHJwKzJ3QmdTTnNES0hPSVlTNHhxTytvTGFjUUNCWDBDNWc5L2NsU1NLQ1NUenVPOGhPV09kK2NqQkNVYkFOQkk0WjhiejZhMjM5Z3M4emI5YXFFNmg5QjdvZGdVeUp3dzBrY2JMNWFKdWxKTkFRbkxSTkRwMVlDaWprNFgzazR2ZUhucldLTXU5TXY0NkUwUXIvRlltVjVJQWttT1pOdDZIdnBYMFpjaFh2aEM4c1l5UVlPcVJHUGsxUHhNMTFPSWNONmdZL1NDSWRuVzlpQkNaQVFJVEtZNTh3djciLCJtYWMiOiJhZDY2ZmU2ZWQ4ZDRkODgyMTNlM2I2OTljYWE1YjI3MDJiNmY1ZGZjNTZlY2YwOTdkYTViY2QzZWE1NGM3NjNjIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjVGU3lhandtVXF3QkhPZVN4bjN4eFE9PSIsInZhbHVlIjoibWlqMTJUVDVIR25OMmFWTlMxZlpXaFRoTm4xSUZWUXN5UjhzL0Q2cG1RMWVOdUlQYzluOUV1TzBPKzlKcnhURjBTbzVDUGZPZFBHZStRL041cWlkdVE5NnllYkRXMkF2Um1yS0ttR2w2cjB1SVF3NDF0cWJMUnQwN01jdTJOQ0NmbndNRzQvWEZxek1sWExNQ3V1Qk5HS0J6MGFXbHo4bHA4cUZ6eUh3K1NjU24xTlF1RXdIUUxsa0xiaDJDcVFLUElJNmpYYXQxVmVJYy9VQldvUXFIeWhtUFp3RGlMNVIzcjlqSWVma2Q3UWZNUi84bGNMQnR2bGllRTdYWVJQdkxvblNXam53SjhocVV5dU5Pem0zM2lYZVRZU0doMGp4QnlxcmczRWZ5azhVdkdqZFFJWjJCd0o0QzM1NFdhV2pvQldRdXpPWUhJRmJEYmx5dFROZ0oybStveEN5QnpyYmNJa1VjUXRLd1FFSnhiQnR3YlpRSUlvU1MvR0EzZVR2NS9uSUlNL3pnbHUzRXVqOVI2d0t3aWRWNkZpNXBUb3BZYlp6SDNwbkJmRmpsYjFNaGZtRGp0eCs5MVFwY3NyVTY5Qml6MzdnZ2hNcjNyTTllbDgxR2lUQzBGUDRHYklRNDVyK0c4WXAvVUh5a293RmNCeXJVVUI4ZGRyS0xVa0MiLCJtYWMiOiI2ZjRmMzMxZjFlOTQxMTA1ODlkMWQ4ODBlM2YyOWEzZWFkYWZlMjJhYzM2ZGQ5NzAwNzliY2NiMjFiMDliNmZlIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1475036872\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-5******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5********\", {\"maxDepth\":0})</script>\n"}}