{"__meta": {"id": "X8e1d1d9649f9cd2713cb96b255365715", "datetime": "2025-06-16 05:02:50", "utime": **********.38027, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.402881, "end": **********.380314, "duration": 1.***************, "duration_str": "1.98s", "measures": [{"label": "Booting", "start": **********.402881, "relative_start": 0, "end": **********.064571, "relative_end": **********.064571, "duration": 1.****************, "duration_str": "1.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.064596, "relative_start": 1.***************, "end": **********.380319, "relative_end": 5.0067901611328125e-06, "duration": 0.***************, "duration_str": "316ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03375, "accumulated_duration_str": "33.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.209204, "duration": 0.02997, "duration_str": "29.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.8}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.280207, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.8, "width_percent": 5.956}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3455179, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 94.756, "width_percent": 5.244}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwt%7C0%7C1982; _clsk=1y3ys69%7C1750050167372%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijc5QUphUHdOSnNrR0VLQ2k0dXFabXc9PSIsInZhbHVlIjoieStUcWNaOTAya3pmTit5SDB2RHVRdzhTeWFjR2Fsd0dIY1BOQjBrdXRJdG9xNTdpbGpyWnEzRUw0S2kzc2d3MmZoTGF3VnRhbjBud2Myc1oxUEdET1BGNGZPZTVMbE4rMm1UekkyQWxma2ZZcGg4ZmR6ZWZiRnJXeTZFMkpCK0dLcEk2SFdCQ2s1QU9BYmxIU0JHTHFESGFqVDdZSWdTa3U5RVJzMjF5M2VlNnd4YTZOZ2FTK2JwOVNCbUg0SkRINDBuOEo5amMraWlIbzMrQThYeHlZZm44WUNneENGZ2N2WDJjRVlBK3hNS2JoeXovNWVpN0pZTSsxR2wxVlF0M3dlWGJmVW12WVhMdkNpTlhyVjZWTDh0eDVnWWlYV0ZpQ3NmV0FwMTRMRU5NclRuaTl2Sll2M1pQVnQ5ZDNpdDZ1c0wybUh4ZmQrTHlPTXNiNCtnV2dDeVRBSmowRXZXZlhENXJoOFljb1NscTZaekYwTWhBdXBEZEVkaXlkTkFoVGU5aTVVZWtaSjJOUTZ5WTI3MEQ5aHF0NkRVOVo2aklRMWYxZGFFL2ZYUE84MTJ2NGlQVEk0cWZXV0FLdnhIRlNoNGJwNmJ6MXgzU2dxTFQwbE9UMHR6eEMxbC83M2VPUldXQlFQY0d2dHhUTVFqMlZhRVNHUVB3RUcyUkxHeWQiLCJtYWMiOiIxY2NhYWZlZmQwMWQ0NTI3ZDI0ZGNjMTY1OTI1Y2QwZTgxYTExMTc1ZDQ2N2FlNTlkZTE3MjMwMzAzN2RiYWZmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjNIZnd2a0ZORVhyUWdmdmp3b2xOUHc9PSIsInZhbHVlIjoib2hrZXR1QS9JRkErTlNwK2FtMjBVdUFaamxXa09CSDNvSmduaGtqUkduRnJjQ1ZjUDdkc25QWkhxRzU5dWJHOVdUK0czZUNlUFgxZjVlUW9SckFhTkxRanhzejk5VTJRZmVIK003aUtkUE1ZUldqcjVpZE5weFNkZEwvMXNqMnVUdnM1ZFU0aFI0MHA3WDBZckMrNmtPRkNMd1MzbDV3ZU5EeFZ2YWp6R1k5aEtITXZoOUxSeVYrSC9FYzIveDc2Wm5Rbmd2eVNJd1pxZ2R0UDZGZnNNWlRBOURqTHFORHdQOEVQS1ZSYkVoMlRsZHRJZ1k4WTVDdFd4MzljV0VWNXlDenNySkpMZ2hUMUtnQ2VYUC90NmRHUWt5Nko2MjExcFFHOEFCNGp1bTVvclB3NktEUVRqOStNN2tqRlZBZ0FWWkt0eVZBdHVveWhKZGlqejYwWnd6bUV2b0g1OTBPL2c5WW5FdG41WlFVQm9TWFMvWFVjVHVVRDVMZ0lsaFpUakFva1h4bzNVaHdjaXgxeUtJUzhrQVNHT3JKdTBZb1ZIWTFkWUdMeTBRTTZHd1BQY1JCaGhtamw5c1pyWHA1cnlwSWdUMXYyZHVuazJybU1RYmp0QUtWd1Q5Y05VdmJ4N3JrRmF0NTBxVmVXdkh5QjU2K0FScEQ2aHRROHpNc2siLCJtYWMiOiIyZWExNDY0ZDhiMDljN2QyNDczNjJlNzY5OTZiODNhOGYxMWQzYzQ0NzdiNThhYmNjNWM5NTlmM2IwM2VhMjQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-193963148 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">F52gGvLLY2TuuySQOODF6RGytIRL64hrZr70fxyL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-193963148\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1760436998 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 05:02:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNuRE5vSys4QnJJOHZtODZ0QU9BWnc9PSIsInZhbHVlIjoiUnVJSC9VY29ldmpnaVdhOVF4dUhCdWpMUUNlbFhNQ0xESEtlU1ZnR3dqa2VLYmc0UDNoZVlIWGFpREhSdmxVVGJDVitKZWNrWXlORS9IR2RDeVUweTdhMkRFQmFxZytvTVRXa21UbGVKYm1kVDJxZE1SVThCTFFJRXpVOWJBRDVWaXB0eTViZGx3NU9lLzJ5d0k1VHo2K2VJR2xpRWlvQWo4Wm5IbkhpZ1ZFNzhSeHl4dDloS3k3VTBVWnlCVGNxL2Y1UkUyeHRHRlh3aC84WXIyM3VsVUtWQXVvSzhhbkM0K3Brak5TWjZrNzlITU1mMzY5dDBWcW56cDNKbm1DZG5KdllYYXRMYTlGeU9pNWZUMklIaVRVc1dIWXVZUDZodm51SEtJU3ZQK3BFWGlydkhDbGdBK1BEYUd0d2FSaXc4dHVkM1JYck0xdm8wZjdaK3lJR280a1BBZHlmL0FyMkFydWphdy9rUEhGcHB0dWkxRU9TQkQ3SUVxbWdMdDdNVXJ5aFJHZUhTcFI2a3FzeERTWWZ1d1RSTFFTM0R1VFoyYUxXdVpaNmNuRWZkbk9uSXBCcDY3Qm9Zc2FodGlicm5rbk1OTU45VHpENUZoRG1TeTluT3FoNWd3WS9nemNHYjUwdXRNWWhidE8rV0I3ZXB6NElISjNGaTMyQk9rbW0iLCJtYWMiOiI1MDc4ZjBmODg3OWU4MjdmZDU5OTNhYzkwZDYxZjNlZDVkYWFmNTIzYjhhNjY4M2ViNzI5MjJkNDU1OGQ0ZTVkIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:02:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InVHeEovak1XUHlqcFN0Q2xFNkEyY3c9PSIsInZhbHVlIjoiRmpsQVIzZEFpRWJTa0dlZXFMMjJKK29WZjFMSEphcllpbnVROGljWmoyNk1HZEQ5TjhYTGY4Mkh3U2FiQWJ2MVVjcUx5Zk1yTnZBQkVEUy9JRWJETlJyVlFvWnBPeDNHdmhRM1BZWGpaeFIvTjllNGZEM3V4ZVFtZ3Z3TGVlWnM1MHdQZFo3eHdnSzRlVVpmQnhQK2hWMkZLakJkcHRvMkZvdWJUbkFwODl0TzVoeEZUamhzQkVLUDZkMHVIbEFWTDAwQk5oV2ZhakZQVWV4V2ZRUFdWM2tEb00vUi96NkFMSUxDNEVzcWxHNHFtVE1mdDhRR0s0RnQyOGxqbnpYRGR1SWcyYnd6aGtkZ3NIQVNFbktLbkxweHVwVmNvRjV3QkVINitFeGhLUThNWitSQlE5RWJPeVZJRThJK2MzT3RPL2F4MGc5ckNUWk04ZkpQSU9uTmt5WHdPY08xQTQ5U2ZheUZOc0N2NWY2dDJCTVI5N3Rrc1g3Sm5qNHZjVzZYTVlpMjlhbTQyVFA3cnN2MUk5c0NYckV6YXozMDRNd3hnbVQwTVBMdWlCMVlHRmYxQllzOGhGeTJRbXZhaFhRSm41Q2NWUEdOczdSWTZJWGdvTnR1ZmZUbSt3bTV3ZEZqQWMzVEpreTdKd3IyRHdPR0Fndmtud045b0RON2VOdHMiLCJtYWMiOiI2N2Y0ZWM0ODEyZjViMDk0NWI2OTczOGNjMGQ5NjdmYTlmYWZiYTJlYmQ4MjJmYTQ3NWJiZTQ3OWViOTEzODI0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:02:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNuRE5vSys4QnJJOHZtODZ0QU9BWnc9PSIsInZhbHVlIjoiUnVJSC9VY29ldmpnaVdhOVF4dUhCdWpMUUNlbFhNQ0xESEtlU1ZnR3dqa2VLYmc0UDNoZVlIWGFpREhSdmxVVGJDVitKZWNrWXlORS9IR2RDeVUweTdhMkRFQmFxZytvTVRXa21UbGVKYm1kVDJxZE1SVThCTFFJRXpVOWJBRDVWaXB0eTViZGx3NU9lLzJ5d0k1VHo2K2VJR2xpRWlvQWo4Wm5IbkhpZ1ZFNzhSeHl4dDloS3k3VTBVWnlCVGNxL2Y1UkUyeHRHRlh3aC84WXIyM3VsVUtWQXVvSzhhbkM0K3Brak5TWjZrNzlITU1mMzY5dDBWcW56cDNKbm1DZG5KdllYYXRMYTlGeU9pNWZUMklIaVRVc1dIWXVZUDZodm51SEtJU3ZQK3BFWGlydkhDbGdBK1BEYUd0d2FSaXc4dHVkM1JYck0xdm8wZjdaK3lJR280a1BBZHlmL0FyMkFydWphdy9rUEhGcHB0dWkxRU9TQkQ3SUVxbWdMdDdNVXJ5aFJHZUhTcFI2a3FzeERTWWZ1d1RSTFFTM0R1VFoyYUxXdVpaNmNuRWZkbk9uSXBCcDY3Qm9Zc2FodGlicm5rbk1OTU45VHpENUZoRG1TeTluT3FoNWd3WS9nemNHYjUwdXRNWWhidE8rV0I3ZXB6NElISjNGaTMyQk9rbW0iLCJtYWMiOiI1MDc4ZjBmODg3OWU4MjdmZDU5OTNhYzkwZDYxZjNlZDVkYWFmNTIzYjhhNjY4M2ViNzI5MjJkNDU1OGQ0ZTVkIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:02:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InVHeEovak1XUHlqcFN0Q2xFNkEyY3c9PSIsInZhbHVlIjoiRmpsQVIzZEFpRWJTa0dlZXFMMjJKK29WZjFMSEphcllpbnVROGljWmoyNk1HZEQ5TjhYTGY4Mkh3U2FiQWJ2MVVjcUx5Zk1yTnZBQkVEUy9JRWJETlJyVlFvWnBPeDNHdmhRM1BZWGpaeFIvTjllNGZEM3V4ZVFtZ3Z3TGVlWnM1MHdQZFo3eHdnSzRlVVpmQnhQK2hWMkZLakJkcHRvMkZvdWJUbkFwODl0TzVoeEZUamhzQkVLUDZkMHVIbEFWTDAwQk5oV2ZhakZQVWV4V2ZRUFdWM2tEb00vUi96NkFMSUxDNEVzcWxHNHFtVE1mdDhRR0s0RnQyOGxqbnpYRGR1SWcyYnd6aGtkZ3NIQVNFbktLbkxweHVwVmNvRjV3QkVINitFeGhLUThNWitSQlE5RWJPeVZJRThJK2MzT3RPL2F4MGc5ckNUWk04ZkpQSU9uTmt5WHdPY08xQTQ5U2ZheUZOc0N2NWY2dDJCTVI5N3Rrc1g3Sm5qNHZjVzZYTVlpMjlhbTQyVFA3cnN2MUk5c0NYckV6YXozMDRNd3hnbVQwTVBMdWlCMVlHRmYxQllzOGhGeTJRbXZhaFhRSm41Q2NWUEdOczdSWTZJWGdvTnR1ZmZUbSt3bTV3ZEZqQWMzVEpreTdKd3IyRHdPR0Fndmtud045b0RON2VOdHMiLCJtYWMiOiI2N2Y0ZWM0ODEyZjViMDk0NWI2OTczOGNjMGQ5NjdmYTlmYWZiYTJlYmQ4MjJmYTQ3NWJiZTQ3OWViOTEzODI0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:02:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1760436998\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-963958432 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-963958432\", {\"maxDepth\":0})</script>\n"}}