{"__meta": {"id": "X4049a253c7a1a19edbaa2b2f469ed6d9", "datetime": "2025-06-16 04:58:21", "utime": 1750049901.017364, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750049898.700016, "end": 1750049901.017402, "duration": 2.3173859119415283, "duration_str": "2.32s", "measures": [{"label": "Booting", "start": 1750049898.700016, "relative_start": 0, "end": **********.111193, "relative_end": **********.111193, "duration": 1.4111769199371338, "duration_str": "1.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.111218, "relative_start": 1.4112019538879395, "end": 1750049901.017406, "relative_end": 4.0531158447265625e-06, "duration": 0.9061880111694336, "duration_str": "906ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46082040, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.693238, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.736386, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.96892, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.984479, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.5445599999999999, "accumulated_duration_str": "545ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.223145, "duration": 0.01804, "duration_str": "18.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 3.313}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.251042, "duration": 0.36683, "duration_str": "367ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 3.313, "width_percent": 67.363}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.630754, "duration": 0.00867, "duration_str": "8.67ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 70.675, "width_percent": 1.592}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.699273, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 72.268, "width_percent": 0.321}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.741046, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 72.589, "width_percent": 0.288}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.790618, "duration": 0.14047, "duration_str": "140ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 72.877, "width_percent": 25.795}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.942688, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 98.672, "width_percent": 0.235}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.950051, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 98.907, "width_percent": 0.22}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.974086, "duration": 0.00475, "duration_str": "4.75ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 99.128, "width_percent": 0.872}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rEMkGLQOKgsJHVrqftO2h3y7pwXm6bpmJHmiTPKg", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-23219699 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-23219699\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-910430912 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-910430912\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2077269715 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2077269715\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-202990292 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1873 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; XSRF-TOKEN=eyJpdiI6IkV3dW03ZnJNSUJLN2RlTVk4UkJheFE9PSIsInZhbHVlIjoiT0k3a1c1eDUxWDN1S2JVVUgxcHNUcGxPcWRlSVQ2UzAxWHZZU2pCK1pKOVd2eFM2dmhscjRyb3k2ZE84aXdhQWlCUGx5Q2wwZWhrMmpxNFpIVTIzWVVhOWVLbzFWYytPeXl2cC8weGxDRXFTWmMzazFWQkQ4a0VzdUpuQ0g3MFdsdnhMbGR0M0FvWklFR1lBdjFSdHJ4NWJqcGE3dnQ4Y3RiOHZrMEZMQnBhQTlXR3h0c1hQcXY5cExubXpzb3BXUjVocUJQTmhoZ0l0SUVEMXFzREpZMDdMa3pid3h0SnlYTnAvbWJFL2hITGEzMVViU0ZQYXQ4cDJ5TWpIOW9ES3F5V3ZHSFN6SWM0eHFOUWZ1djBGUjRCd2YybWIyZHhuNDgzU2wwOE9PSnlGRHBpWVFweGNGdkxqazFDcE1zM1BaUkF3a0JUU1BOalVHbThISW95ZDFUc0hhRmhyNWdzSVZaQVJOU2xGenRDN1JidVJIcnZwYVVIZzVobWlaRVIvY1FwSUIzYzNTS3QzNEtRdUZXYTlrNWhVbkYxeHZyQ3FZQVNzUmkzYmxsWFd0MVc2L3NvOVNub1UxbHBKTGVFSXRGK1FSbkltd095em15S1FabTNGL3NXNytjZ0NzVGxwbVlSQWNpY05CYlZOak5uZzQ4eURUSWV3bStMU21iMVMiLCJtYWMiOiJiMjViYjNlZGZlMzk2ZDI5MmU3ZTBlM2QwNjVmOGNhMjA3MDVhNjFlZTNmYzg2NTM2NjU0YzA0MGVkODFlNzhmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkRmbVQ3TnIyeWtzZTgwSzhRcStqZ1E9PSIsInZhbHVlIjoiblIyY2NLeC9TMDZJUDlSeEhRWmpWS0ZEUDZkYUtRblBnZFh2MjcxZDRhVE52V05iZUFNYWFuUzNVNVl6WjJISE04RVNQaUUxcGJRVEU3V3B0VWpjQ2NDMUlRMnpmK2E5Qjd1cXFiZzdBeE9PZU1RSGVncVZaRzBvL3ZvWlBDQjQzc1dNMW5mejRobzNpV1FMQjJQUnRramNveEdYakc4MTY4bUlkanVLN1pwL3dKMjdwWXJjaXQ2TWtaeUhadVZJNnhEWHBBeXdmWkFYblFxK3h3VngxbFBuUTl6VWkzUjdwOUkraElvZWRXMTVHd0RtcnhjRmp5TGF6NUhrM2ZRYVUyWXNsRFFEWjZISExWMHlJT0daQkRhSDNPZnkxTzQ1MlpoTDhEb1QxVi9iNFIwdWp4Rldpd3hHZWd3RnY4cFo1OWF2cnE2YkZTOFhsVncrUkdabFhnWFA2ZzlNazJCUS9uTUtybWNpZzFQZHkrZnNVT04wbkRueVhPMWRTb3RhRDNEeFRnYnY4QnREYzJLVURQZ2VXVHFzd2hWZkJXOGlkYmFKcFN5TnB3c1BrVnc5NmhvVXJhem96RkZMTVpKNy9pNEp2Zy9YMEJrL21UaDFsKzJJL2hld1d1TUVtdVdocUQ2WVhvbTBZbW1mTlVSWU9SdEIzeWxDWFFiN3BMd2siLCJtYWMiOiI0NmY4NzcxZWI4ODMxZjFlMDlhZTVhMTE3NDg3ODM2ZDdhYWU3M2E4YmQ2MmI2OTIzY2JkNWI2ZjczOGI4Y2I0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-202990292\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1790540509 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEMkGLQOKgsJHVrqftO2h3y7pwXm6bpmJHmiTPKg</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yqjhkDu2mCjmx9BF6LLjjhEo71tuIhsIqbLnBgXm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790540509\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1189842362 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 04:58:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlUzSk1EOHNWSWlwaS9IOWYvVTlaeEE9PSIsInZhbHVlIjoiVVF6Z2FkNVlZY0xaRUlRU1BGUHRXY0EwTXhWcThoNVVsTkRIMlBBenpEWE9SRnE2VlFVc3U2OVIwOW1sZjVvTEZRTEJUTVBlbTVUMWxRb2Nyc09nbWlZMzlmeVN6RU43SjlpcllnYnFmeCsyWkRrTDNqRFdXOGpPREJ3M0FQM29wOGtsNXJjZWEyQWZNKzZvSjF6UVFLYm1zWndpdzU4aWl2SE50Q3BBeGMrZG03U1ZYRmNvS3dRVnZWOVBQeE9DRUsyVERzTnpXd0psNCthdDBDS0k0cXFZSDBoV2E3QlpsdDdEcGZVRUlaZE5qYmExV2UzM3BBeFhtNzZKUklIZlNxTUhVdSt3SFZjVG9QcXZiVmFEb1hwMGxIa1JnL0ZBWmxhRGQ3N3BXOVFxMUN6NnFydUhUZzhNQmR6TzZaa0oxS3k3RlBmamtFWU5JK3hHN25HelMwdldqalBpQTE3TGxMSzc3WHpoVVZyQW5YQmZHWHhzZUROLzkxeVNNNElBSHUvSnJDaEplSGN5NTBuU3FTTFBpVjJQVVByRDU0eHRiYnNXMGpYeFpxTjI0V1NXL003eDZrOEdRaVRoQlhaOWdJeUNSaDR2d2U2SXJRS2ZJRXVoMWpiNHNZd2tIUThvVDZheGgyWTJmb2ZrSTRGK2xNdDY5c1R2QlNwdW5YWkoiLCJtYWMiOiI0MDY2NjEyYTMzZGVhZmViYTE4MjgxMzc3YWVlNTNkZjcyMTUwMDU3YWYyYjA4ZWMyZDI5MzY1ZmJlOTU4MmQ1IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 06:58:20 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdhUEpPZ2JLYjYxU2cxcXJJRHBid0E9PSIsInZhbHVlIjoiOG1HQU5uVkxIcmNOdzdLaSt1RGxZZWFkeWg2Z0pHY2dYNlZhWTY2MlMrYkFQZ0I0MDVBQ08vSDA1TG1wZmpjZ3ZwOGI3TG1UWkZwbDJrNmx2R3o0RCtLNXpZeFFoQTFRRXppbnNOTHd4SlJCcm9STnd2NHozMVBEaFFvc1dvdW84UHl3c0xpdVlxYTUzYkd6NTR4UDh4UmRZUlhERXVMMTllREZScXdZS3FiV3NoR21PNDFDajRCdEYxUDFPZEtPMExaTmRiSWdoWjBNbEJOUlFXb2R3d09GWm9qdU9vNmZTd0wrY093ajlWcDVPSHZYWTc3MVIvcXd2YVptUUJXakVSWGJUR3pDZGxSZkw1ZnJ2c1lpbzBGWDBjV20rRWVLWlNKZU9lUHJ0UXk0cVFHZmtYWHRNd2RvR1U2ekxEVmY3ZEFxcjF4Nmt6OVByMTRlOXRoaDA5QmFGZFVsemIvV21TOHJ5N2Frd21mUW41Ly9Ob29GS3JFRm1sd1BEOXNnemxYdkUyZTZUeGFzd3hvTmdRN3RkTGpnZWw0dUsxRnB5cHJSbG1TcTRWNGZMRjBQUHpseS9uUUQvRkduTFUrZTN6TVBkeUI3dVJPQXN0VTBvWUcya05lOFV5NStwWHFlQWk3R1RVM1JLZWJpRXdMSXRxVlo4WnNwVXNiNW1RVzUiLCJtYWMiOiIyYjQwOGVmZmMzODU5NzQ3OGQ5MTc0N2ZkYzY2NzA4NzYxN2Q2YTgxOWNhNmMxYWI4MzRlNzI0MWY4NjE3NGI1IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 06:58:20 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlUzSk1EOHNWSWlwaS9IOWYvVTlaeEE9PSIsInZhbHVlIjoiVVF6Z2FkNVlZY0xaRUlRU1BGUHRXY0EwTXhWcThoNVVsTkRIMlBBenpEWE9SRnE2VlFVc3U2OVIwOW1sZjVvTEZRTEJUTVBlbTVUMWxRb2Nyc09nbWlZMzlmeVN6RU43SjlpcllnYnFmeCsyWkRrTDNqRFdXOGpPREJ3M0FQM29wOGtsNXJjZWEyQWZNKzZvSjF6UVFLYm1zWndpdzU4aWl2SE50Q3BBeGMrZG03U1ZYRmNvS3dRVnZWOVBQeE9DRUsyVERzTnpXd0psNCthdDBDS0k0cXFZSDBoV2E3QlpsdDdEcGZVRUlaZE5qYmExV2UzM3BBeFhtNzZKUklIZlNxTUhVdSt3SFZjVG9QcXZiVmFEb1hwMGxIa1JnL0ZBWmxhRGQ3N3BXOVFxMUN6NnFydUhUZzhNQmR6TzZaa0oxS3k3RlBmamtFWU5JK3hHN25HelMwdldqalBpQTE3TGxMSzc3WHpoVVZyQW5YQmZHWHhzZUROLzkxeVNNNElBSHUvSnJDaEplSGN5NTBuU3FTTFBpVjJQVVByRDU0eHRiYnNXMGpYeFpxTjI0V1NXL003eDZrOEdRaVRoQlhaOWdJeUNSaDR2d2U2SXJRS2ZJRXVoMWpiNHNZd2tIUThvVDZheGgyWTJmb2ZrSTRGK2xNdDY5c1R2QlNwdW5YWkoiLCJtYWMiOiI0MDY2NjEyYTMzZGVhZmViYTE4MjgxMzc3YWVlNTNkZjcyMTUwMDU3YWYyYjA4ZWMyZDI5MzY1ZmJlOTU4MmQ1IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 06:58:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdhUEpPZ2JLYjYxU2cxcXJJRHBid0E9PSIsInZhbHVlIjoiOG1HQU5uVkxIcmNOdzdLaSt1RGxZZWFkeWg2Z0pHY2dYNlZhWTY2MlMrYkFQZ0I0MDVBQ08vSDA1TG1wZmpjZ3ZwOGI3TG1UWkZwbDJrNmx2R3o0RCtLNXpZeFFoQTFRRXppbnNOTHd4SlJCcm9STnd2NHozMVBEaFFvc1dvdW84UHl3c0xpdVlxYTUzYkd6NTR4UDh4UmRZUlhERXVMMTllREZScXdZS3FiV3NoR21PNDFDajRCdEYxUDFPZEtPMExaTmRiSWdoWjBNbEJOUlFXb2R3d09GWm9qdU9vNmZTd0wrY093ajlWcDVPSHZYWTc3MVIvcXd2YVptUUJXakVSWGJUR3pDZGxSZkw1ZnJ2c1lpbzBGWDBjV20rRWVLWlNKZU9lUHJ0UXk0cVFHZmtYWHRNd2RvR1U2ekxEVmY3ZEFxcjF4Nmt6OVByMTRlOXRoaDA5QmFGZFVsemIvV21TOHJ5N2Frd21mUW41Ly9Ob29GS3JFRm1sd1BEOXNnemxYdkUyZTZUeGFzd3hvTmdRN3RkTGpnZWw0dUsxRnB5cHJSbG1TcTRWNGZMRjBQUHpseS9uUUQvRkduTFUrZTN6TVBkeUI3dVJPQXN0VTBvWUcya05lOFV5NStwWHFlQWk3R1RVM1JLZWJpRXdMSXRxVlo4WnNwVXNiNW1RVzUiLCJtYWMiOiIyYjQwOGVmZmMzODU5NzQ3OGQ5MTc0N2ZkYzY2NzA4NzYxN2Q2YTgxOWNhNmMxYWI4MzRlNzI0MWY4NjE3NGI1IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 06:58:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189842362\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1012672254 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEMkGLQOKgsJHVrqftO2h3y7pwXm6bpmJHmiTPKg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1012672254\", {\"maxDepth\":0})</script>\n"}}