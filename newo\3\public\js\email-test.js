/**
 * Email Test Functionality
 * إدارة اختبار البريد الإلكتروني
 */

$(document).ready(function() {
    
    /**
     * التحقق من صحة إعدادات البريد الإلكتروني
     */
    function validateEmailSettings() {
        const requiredFields = {
            'mail_driver': 'برنامج تشغيل البريد',
            'mail_host': 'خادم البريد',
            'mail_port': 'منفذ البريد',
            'mail_from_address': 'عنوان المرسل',
            'mail_from_name': 'اسم المرسل'
        };
        
        const missingFields = [];
        
        for (const field in requiredFields) {
            const value = $("#" + field).val();
            if (!value || value.trim() === '') {
                missingFields.push(requiredFields[field]);
            }
        }
        
        return {
            isValid: missingFields.length === 0,
            missingFields: missingFields
        };
    }
    
    /**
     * التحقق من صحة عنوان البريد الإلكتروني
     */
    function validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    /**
     * عرض رسالة تنبيه
     */
    function showAlert(type, message) {
        if (typeof show_toastr === 'function') {
            show_toastr(type, message, type);
        } else {
            alert(message);
        }
    }
    
    /**
     * تحميل نموذج اختبار البريد الإلكتروني
     */
    function loadTestEmailForm(url, title) {
        const validation = validateEmailSettings();
        
        if (!validation.isValid) {
            showAlert('error', 'يرجى ملء الحقول المطلوبة: ' + validation.missingFields.join(', '));
            return;
        }
        
        // إعداد المودال
        $("#commonModal .modal-title").html(title);
        $("#commonModal .modal-dialog").removeClass('modal-sm modal-md modal-lg modal-xl').addClass('modal-md');
        $("#commonModal").modal('show');
        
        // عرض مؤشر التحميل
        $('#commonModal .modal-body').html(`
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل نموذج اختبار البريد الإلكتروني...</p>
            </div>
        `);
        
        // إرسال البيانات
        $.post(url, {
            _token: $('meta[name="csrf-token"]').attr('content'),
            mail_driver: $("#mail_driver").val(),
            mail_host: $("#mail_host").val(),
            mail_port: $("#mail_port").val(),
            mail_username: $("#mail_username").val(),
            mail_password: $("#mail_password").val(),
            mail_encryption: $("#mail_encryption").val(),
            mail_from_address: $("#mail_from_address").val(),
            mail_from_name: $("#mail_from_name").val(),
        })
        .done(function(data) {
            $('#commonModal .modal-body').html(data);
        })
        .fail(function(xhr, status, error) {
            console.error('Error loading test mail form:', error);
            let errorMessage = 'حدث خطأ في تحميل نموذج اختبار البريد الإلكتروني';
            
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            
            $('#commonModal .modal-body').html(`
                <div class="alert alert-danger">
                    <i class="ti ti-alert-circle me-2"></i>
                    ${errorMessage}
                </div>
            `);
        });
    }
    
    /**
     * إرسال البريد الإلكتروني للاختبار
     */
    function sendTestEmail(form) {
        const formData = $(form).serialize();
        const url = $(form).attr('action');
        const emailInput = $(form).find('input[name="email"]');
        const email = emailInput.val();
        
        // التحقق من صحة البريد الإلكتروني
        if (!email || !validateEmail(email)) {
            showAlert('error', 'يرجى إدخال عنوان بريد إلكتروني صحيح');
            emailInput.focus();
            return;
        }
        
        const submitBtn = $(form).find('.btn-create');
        const originalText = submitBtn.val();
        
        $.ajax({
            type: "POST",
            url: url,
            data: formData,
            cache: false,
            dataType: 'json',
            beforeSend: function() {
                submitBtn.attr('disabled', 'disabled').val('جاري الإرسال...');
            },
            success: function(response) {
                console.log('Response:', response);
                
                if (response.success) {
                    showAlert('success', response.message || 'تم إرسال البريد الإلكتروني بنجاح');
                    $('#commonModal').modal('hide');
                } else {
                    showAlert('error', response.message || 'حدث خطأ أثناء إرسال البريد الإلكتروني');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr.responseText);
                
                let errorMessage = 'حدث خطأ أثناء إرسال البريد الإلكتروني';
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                    // معالجة أخطاء التحقق
                    const errors = xhr.responseJSON.errors;
                    const firstError = Object.values(errors)[0];
                    if (firstError && firstError.length > 0) {
                        errorMessage = firstError[0];
                    }
                }
                
                showAlert('error', errorMessage);
            },
            complete: function() {
                submitBtn.removeAttr('disabled').val(originalText);
            }
        });
    }
    
    // ربط الأحداث
    $(document).on("click", '.send_email', function(e) {
        e.preventDefault();
        
        const title = $(this).attr('data-title') || 'إرسال بريد إلكتروني تجريبي';
        const url = $(this).attr('data-url');
        
        if (url) {
            loadTestEmailForm(url, title);
        }
    });
    
    $(document).on('submit', '#test_email', function(e) {
        e.preventDefault();
        sendTestEmail(this);
    });
    
    // التحقق من البريد الإلكتروني أثناء الكتابة
    $(document).on('input', '#test_email input[name="email"]', function() {
        const email = $(this).val();
        const isValid = validateEmail(email);
        
        if (email && !isValid) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
});
