{"__meta": {"id": "X09c5b1e349b49502347c3ed0ddf02ed2", "datetime": "2025-06-16 05:04:07", "utime": **********.606798, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.469043, "end": **********.606841, "duration": 2.****************, "duration_str": "2.14s", "measures": [{"label": "Booting", "start": **********.469043, "relative_start": 0, "end": **********.245514, "relative_end": **********.245514, "duration": 1.****************, "duration_str": "1.78s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.245547, "relative_start": 1.****************, "end": **********.606846, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02766, "accumulated_duration_str": "27.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.429252, "duration": 0.02407, "duration_str": "24.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.021}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.498595, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.021, "width_percent": 5.278}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.569241, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 92.299, "width_percent": 7.701}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwt%7C0%7C1982; _clsk=1y3ys69%7C1750050242430%7C3%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlJOVN4M0FhNzFreWdDRHdWMkhhbnc9PSIsInZhbHVlIjoiQTZEalQ1ajUwVUZrQTJJOTlVVm9MeUpZY01kQ2lrM2llTHNQWUdZOElnK1E0MHdTT2lIMStKOGkwMytPT3IxY1lXNFJFU3dZMVRIRkRmb2RsRXBSY3pnaEo5MnJCMXJ6N1pRWlEyNHIrc2h1Tm5zQXVtV1kyOXNjemo0U1Q2RUJHNzdZOFhJQzBpV202TXlReW1Ja05ITTRQV1FOOGpJTytXZWprbU5KRERVQVZxNllRRWNNaUNQTnEvWmRpR0NNTEo2VFlqV1J3UnlGcEpNL2RselZqdnVqaUtKOXVXVDFzWTducVU3R3dJUHhIVENHOEViN09iOG1WZEEyalk5Y2RXTEkwRFlPdHNVYVpld01vNnFQSTIvNUVrTUQ3U2R4TzRqQTJaNUFPNjB4NzJvWUhNRGxqRVAyVFpJQ1NsSWg0cjhlY21vZDZLRWhJS0dQOXV5KzNLT2tEVHVlK0dKdXhBKzc4SmtSVzlGZGRYK3lkVERkU202ejR2TENDVHBLZytZYzdIaWNXdFFaa0NDTHhJV3RHSEhldjZUV28xV0hKUkpOT2ljdXJTck9EVzFUK09uUGU3YXJPNDlacVdlZmxjZU5ldjdtNWVPZ2Vra25PbWdOd2hkNHZ1UUlGZC8yckI4WTZQWnova2ZjY1ovZ3NmMDVzVFNmWnJ0eDREZDQiLCJtYWMiOiJiMjk1NzQ5NWM2YWNiZTQwYjg4N2MxY2UwYTAwN2QwYWRlODYxMjk2NThhOGYzZDA1NTI1ZDg0YmU1YWFkZDY0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlM0VWRRUGpDRm4yVU5PeHluaWFlRUE9PSIsInZhbHVlIjoiV29LTWhsZ1dyZUE2STA5d2o5bkpocEJGTmxrQmVoQWxxb2pkai9wbTE0LzJzU1hYU3NBZnV4TlQ1TVF1TEhuK0IxNjhCOHFYazBKaVJnZUs0MWRPYU90ZGVsWjQzQTRiNHpLdzl2MWltZTNZd1RpdUhPeTVhajdmMmdxSXdFUFoxTnB6Zk55QldWcWtxSVZrZlBjS0RoUmZGYUpjL2FTT3I5NXNqb2JNeFI1UE96bzNEYm15ZGJyaENpQWxQS1M1cHo0a2U3TTFTSGZGUHMvQkhOS0I4L0kySFd3K3ZlL21tWFlCdy9TNjE4dmdXUy9rc0dCdmdqeTl1ZDY5b2RmRFZraWU5b0JOejVhbFU3U3ZLd29ST3paZFNGOFpvd0lxNEQ3L0pMWHNtMGt1aG84WGYzY2FYWThiS1BFNUl4TjNlVUYwdlFhRGJtd0UxUU5hb28xdTBWYlBJUlQ1WURlUmoyQmVTK1habFpXY21ZUko5Q3ZwS1BsRURHOXNMSDRzYm04VFZWVUEzSGJtTFJaVFhGMnhhMWFjS29zcXAxRm5jVUUyNTlMMGRSY0FxaHRwYjhGeExEd1BiUCtiQlhiMCt0aFVYVjBabmw3ZzRIYVlFaGFSbkpDZUxCRVVuYUwrRDk1amNnOEdhOWxJcnc5Z2hmbVJINmkySzBGYUpJRmYiLCJtYWMiOiI1YTczMzE4ZDk3Zjg0MzU3MDRkNzg1ZWFlM2MxMmYyMTNlZGQ5ZGE2OGUwMjZkOGEzYTZkYThlZDE2ZjJmZGQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1647090338 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">F52gGvLLY2TuuySQOODF6RGytIRL64hrZr70fxyL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647090338\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-902036330 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 05:04:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtOeUovQkM3NEhkTHFiRE05M3VKd0E9PSIsInZhbHVlIjoiUWxvWm9lUEgzR09PcHJqWFVWU0d4Vzk2bStydmovc2ZEQWhKUVlmcCt0Y3B4Z242SnVDNHBCL3V1TDdSaW1FRURWL0NGY09yL3RtaUZKN0hXaGpNN1c5bkoxanNxL1ZHT1AwUFlidHNpN0pIRGFtOHpOdm1QdVJaTndXeUZXeSs2NXZRSHVSeUNQQzF6eTNTZUl0MUFtc0QrNlF0Y3Npc2dyTUsrSWgxaHozendma29WclFrZjFlR0E4cGFrWVByZythaHpWa2ZKd3oya0JxT0xJb1ZMbS90dnQremdFM2s4MnB2SGRCMTNqU3lQaHU5dTdvdFQ5ZnNrY2Q2elNnU1hVQi92eWkyVEN3TkpUeDR2U2lud1FpL3pBYkFBYk5BRGFWc1ZLYUx0YjU0QnVxa3RLZ1JvN0xvTFgzMFovU2swTlpvZ1cyT1VTaUkzRkJOMFUrMFRSMDdlOEZUbGpoY1NtemtzQzdhYXBDanZtU3E2dVowUDdZSlM2bnVMZTRTTGROT2psbU9kRkRCNkFLZ2ZReGFRcnJvZzFvOHIrTTcyQnFzRGdDRkNaRjdiNndCS2NTUVhTMlZRalU3MmpoWGRHUW44MGxPUU9pVFVHZ1BaMFdYTmM5ay9pQkEzRCt6VFhrWlkxc3p5QUZNV2lISlVNN3o2a05SWlhuclpaeHYiLCJtYWMiOiJmNDRiZjE5ZDhhOTAwY2VlOGUyODQ2MjI3YjNiNmFkNDM4NmQwYjVmZjMxN2U2Y2E0Mzc1OTM0YjA2ZjhiZTE0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im5IbE5sdVJLVS94eDJyWWFia2QzNUE9PSIsInZhbHVlIjoic2w2Y0dLbEk3RVlpMzRqMmh4c3h5TWJyTmpvZnB4eXBCZ2lERHQrQzhEYUhkY09pR25IU0VkdzZ2VkdJTk5IMlA3SEIyOE8vdG5Ga3FZVHZ1V3N3Z2hOeFd2VlBxV0FWT3ZjNGpiTy9wZ3pKL3BxMk1keHZIU3duYXJ3QTNrTVl2bFVTamVOR24zWUF1bDJ5ODBGUnlYUzlDRG85bEI0d2ppZUR2THdpMzNNWHdrR2ViRDA4dDEwTXpha3k3ekFpakZSSjQwWVhndWdZbCt4b0ZHd2wzMzJoZ0hpMFI3T0dWclVBR3cxOHl3NTl3WmEydGFLcnluNkVrWmsxSXdyUjY0MFozejVMdEYxY1NJMHQwdDhnSnpLQ3V4TWZUYjdHNUxXbXJML2hYMnVjYnhtck9Ua3VWNjRPaFhYL09oQnRNOHBQRWQ0QWZMUndyK1UwVk9lVjVQV0tTcXVyditaTkdiUm5JVHhvYUp0M1A2VFYrM3VIc1hYWlZ3aUZTcU5tMVphTndjNUg1cGxtSDQ0V3RYQjZDd1pBN3NTSDR3bzUxVS95M1B4ZDlibHZBb1RCcTJIKzg2Z2YyMjkrd3kzaUxrN0VydGZkd3ljU1RpMy9KejFuUkMxOTdIY1Z1K2ZEc2YyNm13OW5tTUJWYnZvT0RGdG9CcUszVkRZS25xSmgiLCJtYWMiOiIyMDEwZmRjOGE2MGZkMTYyM2VkNTZkZmRlYmMzMTEzY2Y0MGY1NzVmYjFkZGI3NDQxM2Y3M2Q2YWNhZWMyMzM2IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtOeUovQkM3NEhkTHFiRE05M3VKd0E9PSIsInZhbHVlIjoiUWxvWm9lUEgzR09PcHJqWFVWU0d4Vzk2bStydmovc2ZEQWhKUVlmcCt0Y3B4Z242SnVDNHBCL3V1TDdSaW1FRURWL0NGY09yL3RtaUZKN0hXaGpNN1c5bkoxanNxL1ZHT1AwUFlidHNpN0pIRGFtOHpOdm1QdVJaTndXeUZXeSs2NXZRSHVSeUNQQzF6eTNTZUl0MUFtc0QrNlF0Y3Npc2dyTUsrSWgxaHozendma29WclFrZjFlR0E4cGFrWVByZythaHpWa2ZKd3oya0JxT0xJb1ZMbS90dnQremdFM2s4MnB2SGRCMTNqU3lQaHU5dTdvdFQ5ZnNrY2Q2elNnU1hVQi92eWkyVEN3TkpUeDR2U2lud1FpL3pBYkFBYk5BRGFWc1ZLYUx0YjU0QnVxa3RLZ1JvN0xvTFgzMFovU2swTlpvZ1cyT1VTaUkzRkJOMFUrMFRSMDdlOEZUbGpoY1NtemtzQzdhYXBDanZtU3E2dVowUDdZSlM2bnVMZTRTTGROT2psbU9kRkRCNkFLZ2ZReGFRcnJvZzFvOHIrTTcyQnFzRGdDRkNaRjdiNndCS2NTUVhTMlZRalU3MmpoWGRHUW44MGxPUU9pVFVHZ1BaMFdYTmM5ay9pQkEzRCt6VFhrWlkxc3p5QUZNV2lISlVNN3o2a05SWlhuclpaeHYiLCJtYWMiOiJmNDRiZjE5ZDhhOTAwY2VlOGUyODQ2MjI3YjNiNmFkNDM4NmQwYjVmZjMxN2U2Y2E0Mzc1OTM0YjA2ZjhiZTE0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im5IbE5sdVJLVS94eDJyWWFia2QzNUE9PSIsInZhbHVlIjoic2w2Y0dLbEk3RVlpMzRqMmh4c3h5TWJyTmpvZnB4eXBCZ2lERHQrQzhEYUhkY09pR25IU0VkdzZ2VkdJTk5IMlA3SEIyOE8vdG5Ga3FZVHZ1V3N3Z2hOeFd2VlBxV0FWT3ZjNGpiTy9wZ3pKL3BxMk1keHZIU3duYXJ3QTNrTVl2bFVTamVOR24zWUF1bDJ5ODBGUnlYUzlDRG85bEI0d2ppZUR2THdpMzNNWHdrR2ViRDA4dDEwTXpha3k3ekFpakZSSjQwWVhndWdZbCt4b0ZHd2wzMzJoZ0hpMFI3T0dWclVBR3cxOHl3NTl3WmEydGFLcnluNkVrWmsxSXdyUjY0MFozejVMdEYxY1NJMHQwdDhnSnpLQ3V4TWZUYjdHNUxXbXJML2hYMnVjYnhtck9Ua3VWNjRPaFhYL09oQnRNOHBQRWQ0QWZMUndyK1UwVk9lVjVQV0tTcXVyditaTkdiUm5JVHhvYUp0M1A2VFYrM3VIc1hYWlZ3aUZTcU5tMVphTndjNUg1cGxtSDQ0V3RYQjZDd1pBN3NTSDR3bzUxVS95M1B4ZDlibHZBb1RCcTJIKzg2Z2YyMjkrd3kzaUxrN0VydGZkd3ljU1RpMy9KejFuUkMxOTdIY1Z1K2ZEc2YyNm13OW5tTUJWYnZvT0RGdG9CcUszVkRZS25xSmgiLCJtYWMiOiIyMDEwZmRjOGE2MGZkMTYyM2VkNTZkZmRlYmMzMTEzY2Y0MGY1NzVmYjFkZGI3NDQxM2Y3M2Q2YWNhZWMyMzM2IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-902036330\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-749922123 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-749922123\", {\"maxDepth\":0})</script>\n"}}