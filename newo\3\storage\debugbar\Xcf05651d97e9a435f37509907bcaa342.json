{"__meta": {"id": "Xcf05651d97e9a435f37509907bcaa342", "datetime": "2025-06-16 05:04:03", "utime": **********.633958, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750050241.802948, "end": **********.634002, "duration": 1.8310539722442627, "duration_str": "1.83s", "measures": [{"label": "Booting", "start": 1750050241.802948, "relative_start": 0, "end": **********.348595, "relative_end": **********.348595, "duration": 1.5456469058990479, "duration_str": "1.55s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.348626, "relative_start": 1.545677900314331, "end": **********.634007, "relative_end": 5.0067901611328125e-06, "duration": 0.2853810787200928, "duration_str": "285ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45189800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01156, "accumulated_duration_str": "11.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.450044, "duration": 0.00578, "duration_str": "5.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 50}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.498159, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 50, "width_percent": 15.398}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.573149, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 65.398, "width_percent": 18.08}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.599733, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.478, "width_percent": 16.522}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-466294790 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-466294790\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-340427243 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-340427243\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-349886987 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-349886987\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1562611575 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwt%7C0%7C1982; _clsk=1y3ys69%7C1750050167372%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImppTHd5Um01WS9zdzB0alNiZlNyR3c9PSIsInZhbHVlIjoiMU9xWHRPY2xYQzlZc2xmUVVKM2FzcyswNHFzcHRnVlBJa3hNekpGV1J6eHhjVFJFeUdIOFRMV1E3UGl1cjBHdC91TzRyenBUeFFGOG5NYXl6MHFRaWd1K1g1NENmMEg3cFlrTGxaUUhDc2xqNTNrZ2E0ckNpTTdDRG82cGpZblF2WnZ1RVFvMG0vU1I5ei9MdTUzMkNkN256SmR5VlVVOWRIbS9ncmFlTlh0YkJTK3hsSkV0dnhIdkVxemRwQnVhOTR1OGsyeFdqUXhqc3ZNQjJPZ3RJd3I1UkxkL2FjUGt3d2hOSWF1NTdQaU1JRmRGbVJQQzBkenJKcjNnM0xjS2pZVGFvT3pJdi9KdFF2cnErVlQ3RHNPUlRCVSsxaW5adzlzdG9uaEdwWXFvdndGZ3NueW4yaEdPaEJGa0RGRTZidFlFRFNQU241TG13V2lhWjUwelAzcUxsRUpFa2QybDRFdWg0TkZtSXBZL3hPUEdBZGVFMGtHZm5xbU95REVlODRUbFZlaFhHZXdsYlh6bnZXa2lOV3VZVHc3WmVnVlMxc2VCRG52MnJqUjVLdmFCV1ZxMlZ5ZXMvdU1uR3V0RTZvR2hkckVmQ1BtcERsN1M3Q1llMWtGb014MHl6Qm4yc0dGbzJ6WjZIU3hvZWU5UjI5Z1pLM0FxS1dHOGRVdEsiLCJtYWMiOiJhMTExNTU3Y2ZhYzk4MzE4OTdlNGJlMTEwOTA0MzgxZDIyM2EyZjg3OGMzYzc2MzIwNjJkMTc5MTNlYjU5ODZiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJpYVlHWk1ueUJzSlFSWGMzTzJzYmc9PSIsInZhbHVlIjoiS3FMcGo1bG1lSE5ETnd4WFpzaFl6NTJnVm0vMGFMN0s1TzltcThtZCtKNnlCUEpjZFoxNDdUQS9WM1ZQejY0ZzRLbFZpOHlDZnJ6UWVqZ0xmK2d0QmxWS2tNWWZndW0rU29FZWZXejhZbCsxdUdrZFpMeDVldGloanJBcUZDS0tKc2Izd096NW5EUFh4SXhoZWFJTmE3QXlGRG9CNHdjSTRWZlVvVDhvK1QrbFNCY0JPQ25UYVZ6SmM1MitMZFVBTFgvSG1HaWpZNzBXazU3NGFQV01Hbi9hK3hzdFBJNXdSODFQSXhzQVhONkJudXltMXc5Mkw4LzNzTVVqWUVYQ1NNcFZzbjNIWlNJOWlYYStEdGJmT0NNRE1WZm1WZ3Y5N3JJYkp0akJjcmxyNk11UUs2MUdEUkpyS1JQMi9HRXFLZHIwczIwQjFDS0hIL3ZGL0JEWFVuS1oySWlxREtORTVlZHh1UEdqN2pCS04wU1RWZHppL3RlU1UzMXRWMW1lMkljcXBVM3U5WFVvNjJkWWxweUx5Z1VQSC9JemFFUEFDREJOaFd6UEhEcjkwYWxQbklkUlRPTTByRTdHVENVNHVKSGYzRFNqc1BwcE9HeDdLbVFwOWRoNWhrSmIvOFYvdDBSdVNvZEY2QjlxNEVLWGVrNkVYZnd4OVk4dnVXMjIiLCJtYWMiOiJlZGYxZTk2YThkZmVhZjFhZjVlMzg0ZWIxMjdkNjI5N2E3MDgwMjI2MzY2ZjAzY2NlM2FmYTExOWJlN2Q3MDAxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1562611575\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2105783325 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">F52gGvLLY2TuuySQOODF6RGytIRL64hrZr70fxyL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2105783325\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1098873858 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 05:04:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjR1bWh3RG4wZlV5QThtczZuZkQ5Smc9PSIsInZhbHVlIjoiSUFKcDA0V241dHdZVkhwZDJwTE05UEJDK1NNR3Z1dm80N0NTa2M2QVlGVlp6eHIzeFplYlFFYzhSNDJEMENWM3lWNnE3OXJrWWUyV2lldDlFUTBuTXBpaXhnYytHMnJRWkluUWJaeXdCNWN4SWxVQkhUTnROaEtOTTV4OTJOYmMybGFDbEEvWHBrZGZpM2ZERTR0UW9VY2ZSN01vZ3BraC9ZR2tiVGRIdHFNNW1GcXdjQlk0VHFVT2JpUXhWeFU4VUZwbnY2am5yN0ZHaUJmelpzRTZDbGJzWTJYMVFGaGZxU2xGQ3l1YkdXdUliUXJJckowdWtwcS80Y0FHaEhIRlYvSmhKNUpoQkZOSzE0azNlaWwzNVJRYlFFcEtGVTZucS9TbDRucGJBc25Dbno0d3hDMEZmUlpYamphdko1VjVZb29oSGwycm51TURlekllaTZkMm8yMkJiMC9Mc2c5b2lycE85dCtRdlZPT0kwdTI4bVhBV0FHQzJOZXdiTGtVRE5LU2xDVmJid3M0TmhKWDcwK2hBV0ZwWmd5Z282WnhydEtRS3FjVHBkTUF1QUcvWXFyWnlmU3RHcSswY1VhcDBaY3cxOCs3Q2VxWmo0Mk9BbmlWckM3ak1rcjhoM3BPMEQvYmowQWFiS2JPTExyNUpaVi9zTmtnSHFGclVuOWoiLCJtYWMiOiI0MzFhZGI1MmQwODZlMTliZDU3ZGY4N2FiNjMxYTJmOTU0MzAxMjZiYjM5NjA5NjhhNDFlMWE0MjIyYTlhZmQ0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjFQeHhtNVVIdUVwcitWVCtMU002aFE9PSIsInZhbHVlIjoiRS9CSWNKT3U5d1ZVRS9DZ1pMR1daa2x6Um85aDJJQWhBTFZlK2ZvQjlKc3pScDRuSEZvVVFTbVVSQ2g2L1Z1RjFLSnNLZ2V5em9iSWxUaS8zWnlISTd2QVQrM3BsQnZVMGtxOUVXekt4cnR5S0dOOXJqSFhDRndlSW12QjFObGV2cjFFVWdRcDQ2ZlkvUy9sOWFaYThwQk9YUDB2eVRnbVM1VkNFdmJwYzNVdG04NHJGdXYrTlRXWitxNkhZeHBHcDRaQytYbktSeUtWVC80OVRoZlV2TTY1aUZkSGtnUUpWbTRRbFBZMmhhNHFSWXZnOEdrNEJzQXgxVTdvS1BhbzQ1UTl3a1I3MENjaEJNUkR0dWRheEw4NTB0UFR1V3dvV0doTFZNSTZoUVJsQ1FYKzkwQ0FtQTBiTWJYSTg5SE5OdFVlK3MwRmFmZ0lHS3pWV0VmS3pESDJmVmduTElsZEh6S3ZwVkMxQTUyNmJYZVo3Z2RHKzVLQURzTC9kYWN6ZnZXdCtBN1Y3S0wySmJnbmNrcjRuVFM0eXJCdGdLSUpLbm51cGIxWGwzcVFhYzAzaU5CNDQwTmtlZ0tLRk5Gb01BWUNYcktPSklzT1NyR0trWGd0Wm1Bb21pVmdpV2l2TzBuenhWcWhBRERzMlkrOVlJL1hjb3JBbnlaa20xVEgiLCJtYWMiOiJjZGMzMzRmZGNlM2FjMjUyYmIzMzY1NTNmMzlhYzUzYTQ5N2ExNTExZmRkMjlhZWJiNzMxNzE4MGI1MDgxM2JjIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjR1bWh3RG4wZlV5QThtczZuZkQ5Smc9PSIsInZhbHVlIjoiSUFKcDA0V241dHdZVkhwZDJwTE05UEJDK1NNR3Z1dm80N0NTa2M2QVlGVlp6eHIzeFplYlFFYzhSNDJEMENWM3lWNnE3OXJrWWUyV2lldDlFUTBuTXBpaXhnYytHMnJRWkluUWJaeXdCNWN4SWxVQkhUTnROaEtOTTV4OTJOYmMybGFDbEEvWHBrZGZpM2ZERTR0UW9VY2ZSN01vZ3BraC9ZR2tiVGRIdHFNNW1GcXdjQlk0VHFVT2JpUXhWeFU4VUZwbnY2am5yN0ZHaUJmelpzRTZDbGJzWTJYMVFGaGZxU2xGQ3l1YkdXdUliUXJJckowdWtwcS80Y0FHaEhIRlYvSmhKNUpoQkZOSzE0azNlaWwzNVJRYlFFcEtGVTZucS9TbDRucGJBc25Dbno0d3hDMEZmUlpYamphdko1VjVZb29oSGwycm51TURlekllaTZkMm8yMkJiMC9Mc2c5b2lycE85dCtRdlZPT0kwdTI4bVhBV0FHQzJOZXdiTGtVRE5LU2xDVmJid3M0TmhKWDcwK2hBV0ZwWmd5Z282WnhydEtRS3FjVHBkTUF1QUcvWXFyWnlmU3RHcSswY1VhcDBaY3cxOCs3Q2VxWmo0Mk9BbmlWckM3ak1rcjhoM3BPMEQvYmowQWFiS2JPTExyNUpaVi9zTmtnSHFGclVuOWoiLCJtYWMiOiI0MzFhZGI1MmQwODZlMTliZDU3ZGY4N2FiNjMxYTJmOTU0MzAxMjZiYjM5NjA5NjhhNDFlMWE0MjIyYTlhZmQ0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjFQeHhtNVVIdUVwcitWVCtMU002aFE9PSIsInZhbHVlIjoiRS9CSWNKT3U5d1ZVRS9DZ1pMR1daa2x6Um85aDJJQWhBTFZlK2ZvQjlKc3pScDRuSEZvVVFTbVVSQ2g2L1Z1RjFLSnNLZ2V5em9iSWxUaS8zWnlISTd2QVQrM3BsQnZVMGtxOUVXekt4cnR5S0dOOXJqSFhDRndlSW12QjFObGV2cjFFVWdRcDQ2ZlkvUy9sOWFaYThwQk9YUDB2eVRnbVM1VkNFdmJwYzNVdG04NHJGdXYrTlRXWitxNkhZeHBHcDRaQytYbktSeUtWVC80OVRoZlV2TTY1aUZkSGtnUUpWbTRRbFBZMmhhNHFSWXZnOEdrNEJzQXgxVTdvS1BhbzQ1UTl3a1I3MENjaEJNUkR0dWRheEw4NTB0UFR1V3dvV0doTFZNSTZoUVJsQ1FYKzkwQ0FtQTBiTWJYSTg5SE5OdFVlK3MwRmFmZ0lHS3pWV0VmS3pESDJmVmduTElsZEh6S3ZwVkMxQTUyNmJYZVo3Z2RHKzVLQURzTC9kYWN6ZnZXdCtBN1Y3S0wySmJnbmNrcjRuVFM0eXJCdGdLSUpLbm51cGIxWGwzcVFhYzAzaU5CNDQwTmtlZ0tLRk5Gb01BWUNYcktPSklzT1NyR0trWGd0Wm1Bb21pVmdpV2l2TzBuenhWcWhBRERzMlkrOVlJL1hjb3JBbnlaa20xVEgiLCJtYWMiOiJjZGMzMzRmZGNlM2FjMjUyYmIzMzY1NTNmMzlhYzUzYTQ5N2ExNTExZmRkMjlhZWJiNzMxNzE4MGI1MDgxM2JjIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1098873858\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-868663080 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868663080\", {\"maxDepth\":0})</script>\n"}}