{"__meta": {"id": "X0ee6d72687847a9490350924fe50abf1", "datetime": "2025-06-16 05:02:42", "utime": **********.198948, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.21947, "end": **********.198989, "duration": 1.****************, "duration_str": "1.98s", "measures": [{"label": "Booting", "start": **********.21947, "relative_start": 0, "end": **********.855132, "relative_end": **********.855132, "duration": 1.****************, "duration_str": "1.64s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.855162, "relative_start": 1.****************, "end": **********.198992, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0406, "accumulated_duration_str": "40.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0182998, "duration": 0.02991, "duration_str": "29.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.67}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.083795, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.67, "width_percent": 4.803}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.159513, "duration": 0.00874, "duration_str": "8.74ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 78.473, "width_percent": 21.527}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwt%7C0%7C1982; _clsk=1y3ys69%7C1750050149178%7C1%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9zRWtLRlFYc281MzVqQ2pCOFNLWXc9PSIsInZhbHVlIjoienFYMy9uZzJpU2s3QVhIQVptdzlrN1psdjNybnJ3Q2ZHem5mcm1CbmE5eS9GeGVJR0JSeis4TmJQWEV5c3VzVjc2SDVQR21iaTRkT0wzdm9RYk82V1k0eFl0aVdMK1pWYjE5TVpHaGZVeVg5QkIxaVpTSEhKZHlYOERQMENaMndROWVnY3l2Y0FkYnA5YmFPSE9vRGFlUS9zcVBBY2NMcm5QNGszOE1RSjNBVUxtT2ZUWGJrYnlXU05sVlVURjNOQ2VPelVNNW5ybWRjSzFvQ3R4REgzRkZEUGZZWExlcXBiOEVDSjBrbWZ1MWd1MUxnaW9SQmhVSmFZNElsRU45eWplbVZyQlppTEtpdy8wZndoYnp2MGJjTG1vVWpYbTEvdUE1UXY0cEZFUDBCTFRrVGI1U2l5Rm1QdEoxQmNFOUlxTFM4eTdUTXdEQ2ZTTDN3RzRWbzNXb0NvS1R6UkNqZFJwemtFVzZ5RW9CL1MwVzQrVHpRVDJrMkhpTUtMZ2xaTy9hb0RJWUlxa2kzU0d2TWM5OGt0c2tKbEtNb2NJSDB6aE0xOTMwVm0rRzRPb3ZZREhzT29pbVdWa1pSMGxjTXY1eVBIQWh4ajVra0IzM3AwYkw4elBEOWZWbHlNZFBUVEVnVExHRU4zekpvNllZeHhZK3c3dEkvL211b3l3QWkiLCJtYWMiOiIzMmE1Y2FkNjdhMmI0YWZjY2M3MjI5MWIyNWQ5MWZjMTRmZjliZDVkNmI0Yzg4MWViYjY5NGUxZjhhZjVjOTEwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjVtaW1nQ0xFMWNML295MUdmM3kwVlE9PSIsInZhbHVlIjoiNE5IVGZOSm92cnlXWTVPMUwydTYrWG9qTTlyeHpaWTNzcVdjZVg3ZmRpMERXdk4yVlpyN3VKTU0yeUpDRTVZR0FtZ1FGM3RIVW1jbFVUeTJrVko4cHBmcllpLytrdGxRZ0Yyb3VWckYrY0h4YW5zMWZYb0FXTWNKK3RubWdWU3ZtVGR0Mkh0WitQSnVBbC9aWEplU3lQVjFlQU00MmdNYlJMVkR3dDROdWZHRGdxdjAzSTd4ZXptWW1nMXJ3S25jYmFOM2wwVjFOOTZydDFBWnRtZGZYRFJpSVhIVWVrbk1wakFIRklSK1Bxd01pUHdURXQveWxkZGkrVU9BbEVjdlhhSTJJekFSYTYzYnA2SEs4MmxoUEZjVjh6all2K2wxT1NGVFB5aWxUUWNoVXhwYXhKYW8rYVY1ODM5Y041clMwQmdZYVVRK3VBVTRMcU1TMi9JWit1aFlrajZaa1NCSWVFYWlHN2NsMEQvNmxzb0NlT1ArVENteHJZWm1TRitvWHJaeUozOFRXclpubVdwNkFiVWhWZWFJVUdjcWZZalQ0d0h4cEF2TDBCSlFHKzB6aHpXVFdsZC9BWExReFFtRVM2UndpL0QrTE95TVoxbTZWS2tFcHh0SGoyRHRsc1N1ZUkvNjJZaU9nTlR0dEJhUUZEb2JxMStEa2FjVW5WcEEiLCJtYWMiOiJiNjJhMDU3NDFhOGYyOTBkMjFiMmNjYWI0NmQ5OTQ4YmJhYTFmNzA4OTRlMWU1YWMwZmIwOTU1M2IwNmYzNjgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1081372462 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">F52gGvLLY2TuuySQOODF6RGytIRL64hrZr70fxyL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1081372462\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1195380358 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 05:02:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRDMUdQczFNK1oyenNNSDRnOFEzbEE9PSIsInZhbHVlIjoiQWYwQjhlUDZGOGdvMjhDNXl5SkZpcHpCM0xoUnNGY2VWWlRTdDkwK1Z0WGNYYnBXbTFiQTJ5MFg0V1hmTXhNOXdZdi9jYVp4bGtlcGhHYklGc0JDSHEzK3BVN24vNk1ya1JIV2FYbVZkYm9XN3YwTk5ndnZXNHhoNjdDdGxtZWtaUUh4SENzUmpBdDJHYWpHZGFzOTh6R1JmakdVMEprT09Oc1BMT211dzlMVHpyS2RxUDJkQ2N6bmlaL09FL2pDWDl5QUxucHhkSkdVUFVaQjFhQldROTRqditFT2ZJckZ2TFB3NXYzbG9laHZTNTdCK1hKR2MzZTFmbEdLN0ZWTUlvUGx0Tmdyc3ZaYVRYMlJYRmdFeVAvUUc0cVBRVHE1MUE1UkRxMFhxdWR3c2Z1ZGVQOElJN2l6MnJ1VDRna0xJcXdNb2R0a3hMZk4weEw1amtWZ2xTZjJKY0NYcUdHMUZjWDIzUWYwL1hBbXhCdVAxSVdKRzRzM0NHeklCUTVvUGtScGZVaXpSYUVqV25IK0QwY3R4aytLQ2tWLzZJeFo2TVMraXo4Z3dINGNoUXVaSE82UEhYUEN3TmpUNGZndExDMTRKZFdpNHplUEVaZVAwWVNYcFNPWlBXSmFwbVN3ZHl1Ykpna2d4UWwzUHI4cUYyVHJYUnhUck9leENEbFQiLCJtYWMiOiJmNTE3MTFiZjRkNzcyMDcxODViZTk1Yjk2MDZkNjc3NDlhMDcwZDRkN2NhZjQ3OWQxODBkZTY2M2JiNzY0ZjBkIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:02:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkhGUC93Wis3OFdUOXB5Z0hETGlkUlE9PSIsInZhbHVlIjoiV3dud0VnZmUwcStYcTRkS04yR2gzOUNpdTM0WTVCRlhkQVVIdzBwMytwOWc2ZjFzVWd0QVc2cEY5Q0FjVXQ2eEd2dXZndEVhU3dFSWFFU29xa3kvRjBBUjdRWTV6VUlaa3VBTlpDSnlmQUZVek4yUFd4UHh5R2ZEWGorMnVZbW9TSFlqeGh5K0JMU3NRa2l3WnY5K2tqWW8rZUowSnpCM2gvaUp5SDJyL3BlSGtTUDA0cEdvY3J6a05zbm9OUjk5VmJwNTRjUjZjUjQ0ZVRCZVpwQ1RhaVFwQUp3NStnbzJ1SzgwQ2Eza0tCalhVSEsrd2k1M0lQZTlwaTJYTlNjL3JITlpBSDc1dEk0bSsxbXdKVlA4MXBCd1ZiUlpVc3UvWmZKWVJlNFpidTVZRllrSEU3Y0VOQTFSaXlCT2VNYkt6L2NwNVpCTEx0REdnTzBrN0tCcFUxalV4OHI5UlgwaFhEejZCQmNwVnhDK3ZSWXNaSk1NbmludHBtaVB6WU5tN2V4NG5PTGw0V05tdS9WdGNiV1NvQjNDZUhuVEk4NDQ3NFMxb1BXZ3lrT1h4d2E2Mm45SkhwakRBS2NmZCt1Tnl6b3QvUW9FZjZadlQ5a0xzSlRZc1NtdFVhbEFyelovV0d0WWNndWMxS0VpcVZiZzJTdXo0VE1aS2tiWk9sT2IiLCJtYWMiOiJmZjYzMmE1ZTgxYTBlMDBlNmUxNWVkOGRjNGJkMTg3NjhlYzljZTdhZDdiZTlmYWJjODAyM2MyNDFlOGYyODEwIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:02:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRDMUdQczFNK1oyenNNSDRnOFEzbEE9PSIsInZhbHVlIjoiQWYwQjhlUDZGOGdvMjhDNXl5SkZpcHpCM0xoUnNGY2VWWlRTdDkwK1Z0WGNYYnBXbTFiQTJ5MFg0V1hmTXhNOXdZdi9jYVp4bGtlcGhHYklGc0JDSHEzK3BVN24vNk1ya1JIV2FYbVZkYm9XN3YwTk5ndnZXNHhoNjdDdGxtZWtaUUh4SENzUmpBdDJHYWpHZGFzOTh6R1JmakdVMEprT09Oc1BMT211dzlMVHpyS2RxUDJkQ2N6bmlaL09FL2pDWDl5QUxucHhkSkdVUFVaQjFhQldROTRqditFT2ZJckZ2TFB3NXYzbG9laHZTNTdCK1hKR2MzZTFmbEdLN0ZWTUlvUGx0Tmdyc3ZaYVRYMlJYRmdFeVAvUUc0cVBRVHE1MUE1UkRxMFhxdWR3c2Z1ZGVQOElJN2l6MnJ1VDRna0xJcXdNb2R0a3hMZk4weEw1amtWZ2xTZjJKY0NYcUdHMUZjWDIzUWYwL1hBbXhCdVAxSVdKRzRzM0NHeklCUTVvUGtScGZVaXpSYUVqV25IK0QwY3R4aytLQ2tWLzZJeFo2TVMraXo4Z3dINGNoUXVaSE82UEhYUEN3TmpUNGZndExDMTRKZFdpNHplUEVaZVAwWVNYcFNPWlBXSmFwbVN3ZHl1Ykpna2d4UWwzUHI4cUYyVHJYUnhUck9leENEbFQiLCJtYWMiOiJmNTE3MTFiZjRkNzcyMDcxODViZTk1Yjk2MDZkNjc3NDlhMDcwZDRkN2NhZjQ3OWQxODBkZTY2M2JiNzY0ZjBkIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:02:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkhGUC93Wis3OFdUOXB5Z0hETGlkUlE9PSIsInZhbHVlIjoiV3dud0VnZmUwcStYcTRkS04yR2gzOUNpdTM0WTVCRlhkQVVIdzBwMytwOWc2ZjFzVWd0QVc2cEY5Q0FjVXQ2eEd2dXZndEVhU3dFSWFFU29xa3kvRjBBUjdRWTV6VUlaa3VBTlpDSnlmQUZVek4yUFd4UHh5R2ZEWGorMnVZbW9TSFlqeGh5K0JMU3NRa2l3WnY5K2tqWW8rZUowSnpCM2gvaUp5SDJyL3BlSGtTUDA0cEdvY3J6a05zbm9OUjk5VmJwNTRjUjZjUjQ0ZVRCZVpwQ1RhaVFwQUp3NStnbzJ1SzgwQ2Eza0tCalhVSEsrd2k1M0lQZTlwaTJYTlNjL3JITlpBSDc1dEk0bSsxbXdKVlA4MXBCd1ZiUlpVc3UvWmZKWVJlNFpidTVZRllrSEU3Y0VOQTFSaXlCT2VNYkt6L2NwNVpCTEx0REdnTzBrN0tCcFUxalV4OHI5UlgwaFhEejZCQmNwVnhDK3ZSWXNaSk1NbmludHBtaVB6WU5tN2V4NG5PTGw0V05tdS9WdGNiV1NvQjNDZUhuVEk4NDQ3NFMxb1BXZ3lrT1h4d2E2Mm45SkhwakRBS2NmZCt1Tnl6b3QvUW9FZjZadlQ5a0xzSlRZc1NtdFVhbEFyelovV0d0WWNndWMxS0VpcVZiZzJTdXo0VE1aS2tiWk9sT2IiLCJtYWMiOiJmZjYzMmE1ZTgxYTBlMDBlNmUxNWVkOGRjNGJkMTg3NjhlYzljZTdhZDdiZTlmYWJjODAyM2MyNDFlOGYyODEwIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:02:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1195380358\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-10******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10********\", {\"maxDepth\":0})</script>\n"}}