{"__meta": {"id": "Xb998c17af314b3b7a0fabcacb98e3a50", "datetime": "2025-06-16 05:02:52", "utime": **********.143463, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.394173, "end": **********.143508, "duration": 1.****************, "duration_str": "1.75s", "measures": [{"label": "Booting", "start": **********.394173, "relative_start": 0, "end": **********.861133, "relative_end": **********.861133, "duration": 1.****************, "duration_str": "1.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.861169, "relative_start": 1.***************, "end": **********.143514, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "282ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02769, "accumulated_duration_str": "27.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.98448, "duration": 0.02393, "duration_str": "23.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.421}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.045117, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.421, "width_percent": 6.609}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.108696, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 93.03, "width_percent": 6.97}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwt%7C0%7C1982; _clsk=1y3ys69%7C1750050167372%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImR3RWZYaDBob1J1V2Z0ckFYYzNTb3c9PSIsInZhbHVlIjoiREJRL24yd2VNSExjNUdrQ0crQUs1Z21jVU52ZTA0Slk4ako1UG1EYVhnOVJZbUwyYThYRE5CenRPeGdSd3RsdHlFMU44dzNZUXZKKzlrenVKZStiTjJqcDd4aVR1clMyV0JDcCt2VEFFWDBob2dEa3Vnc1h3ZERBZ1RNTklQTXJUV3dtZW5VOXBPY1c3ZHFvZG1xZUdFOWczOUNSTXNMRS9TUHF0aE51eGpiaEVZVldlTlkrQnEzU0pjTnlxWnhndzhXNFAxRWp0ekRiTFFVaVFGSVNDN3YwVGhGN0ZTYnhPcUpkTTkrdTlkN3d3Q2tIcUlSVG1PUGNXKzBqNm80MEp1d1NkV0FFUm1LUllManFSY1ZzTmFLQ0ViMVJDV0Z5UU9lMG1qZ3pLR0hWUGpuQTlpejNrODQ1b1V1VlFPSnNseU9xelZxaU4zZzlDUlJHem5FVnNnTHM1cVFBYnZaSE9UVHZ2cnd4OUExbnJpRUxySXMzeSs4eElIZlZselZSVnZMMk5ZS2JGNm1Xa2JRVzNTVTg0Mzh6V0xLWVhXVWdnaTZoVUZ6V3ZvNGFGQThZM1VtakYrd3FSUE9CdVdoQllvVkd0ZHRHcGlRQU1DWEIxWExhSmdjQW9DbHlJa1BjeE9RdUhnQVVkSCtNamROQ3crZWJudFhGWUNtaTBJZTIiLCJtYWMiOiJmZmNjYTY5MGM4NmI3MWJhZDE5MmQwZTA1NjgxZTIxNzVkNzFjMDBjMTE4NzY2ZTVjNjNjZTM0NWY5NTE3NjFjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ii9wRDNBTFQyRXhHSHovRXNuSWdmU1E9PSIsInZhbHVlIjoiOHpCSnR2azBibDJ4eGhzZ01sZXVFVEJzSmpsNUQxOXgycitLVy9kdkJBRVQyWkRvMnd2NTFwU1RtR2NQSk1aanIxbktyNEdMelZyeXpUT2pnNjBsRmNKRkdWeExyQTVxOWE4TjF4SVQyVjUrYnRVY1dsRTVNYlhDaldTOTJYZVREVEpOWXJOUitocERiRHErM1VHdVVuV05XU1VmQ1lOaTR4a044V3lXRWdNUDlDenJjUEEzZW1iMFpWQWhOaExidEdiWkFvZlpqRkg1cHJEdCttYkdsaFhiMDNuT29iM2pvNWZOcG5aaXdHekRHSElnSVFjTXdGNS82NlArRDVTNE0wVkV2eGcvVXNQMDU2eUdaMmZUZUt0OTJ0S05EQWx1QzNjUUhocnVTWVl5U3hJd21Kd0txcGpBSUZYc0FxSE9sZ2ZpK0VuR05ZZ0dkSlRqUkhWakJQQWJCbzZ4ZVcySHlBNXRFUDFWV3dmVkowTUx3NzJhZWQ0UlBMMmRjbm9EYXdtZ3JBLzA0RVFtV2lzbGRTTXVlZ2JRaUVTbm5lSEovZExQOE5ZWXM2U2g1ZklJeWlKWnNWUFk3dmlvdDBHWmZ5d1c3Q3dna0ZuVG5iREh4bHFTQXhSdUNpNmhYTU5qNUZNUlFRYVlEdUhhNW9hL0Mwbi95U0UwU0dNYVJ4NmUiLCJtYWMiOiI1NGM0ZWRjYWNkZDhlNDNmMjA0NGY2OGIxYWZiYzdjNzhlODg4Nzc2NTY1YTg2MjlmZjhmZDNiOTRkMjY2MDcxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1774530924 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">F52gGvLLY2TuuySQOODF6RGytIRL64hrZr70fxyL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1774530924\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2053636831 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 05:02:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZlZHRVR3VWd0g1V01BRUljSHpsR3c9PSIsInZhbHVlIjoiZjF2TDMrTDdRSGNScmFhWllEQW92Z0NUNTd6Y2JiSnZMZ0hIZ01YUHdndDVOOWJZNGk5b0VNQ3NjTTNxK1g3SkJTZUd0ekRZQSs1S2E3SkxRVFA4c0IyWDdWTDFXU09CTHlHaUJEaGtlV3B5d29tSkh6UlRKVE93TjNyaXlGTldlOFh3MjdBaHdMSCtCbUNKUlVROTY1aE5YZHdqbFhCeVp1OStjQ1hCcXFuY0NTT1NOamdjU01kajZSdjd1OE50bkRiTHlTMEVJMkhad1ByRGZKS2czZVZMWms0dmUraURYUTlJQ2xSL09GdThVRTRtcnJzMmlCNW5EcmRzd3VVZW12diszaWRtYk42cC9yMzZhZ1J5dlA3OEhYLzlvRnRkNjlGY1RLSEd0d0pEUHg2b3VacThxRkprSnk4aUJYTkVUajFBMjduOExpM3NBaExXMFNya0VOYkEyYTdlR3ZKc0VWZlJPN3BpNmRWUkhMMGNXM2ZZd0ttQVNEOFgwQlBwQjFOVCtkNEVGTUdaYURTZnFSZzFNay9kekI5bjF3OGsrdzFPNjU1Y3FEZnFLS0FTczhzNkE1eE9rYmE1ellqVFQ0d2I3OUZTd1JDQ1ZXMi9udnFRRGNLRXhpc1NkSXg1Z3VVbjNJM3pRY2RSa3pPaSt0cEpLYVd0ZHpMcm9EL24iLCJtYWMiOiJlMzczN2VmNGNiYzQ4MDZjNmM5NDM5NDI5YjI5YzQ2NjAwNGI2MDcwMTAxZTRhZTZjY2E2ODI2NWQ5ZDYwNDExIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:02:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNSeUs5U080c3Z5dmtMV0NHVFVXYlE9PSIsInZhbHVlIjoiL0tPNU1KV2xnUG5NQ1JkZkEwalN2WmVQMDJocHlOVElhaW8zMWZubThRSFBEMGhtdGp1cHBCUGx2NjB5ZWtQdFN6UTZCQlVWS0hkRGR5UFJqUkI5d0twTjFEVDU3TWI4MWY0ZW9aVFFQWWFvZ29sd1ppcTFXcXFDYmwwejYyaUk0ekV1RFdVY2Q1SHBDanFSbHd6ajkyYmhqVEJGeU13SEpaNE1GTUZLQnFMZUtscGcrM3JVdUJxemFyaFp3S2EyTzZWbUpWUnpZdGRiVW1ydUZkY2huL2hoUnZqZCs5VkkvSHQ1RXhGODBVajl4NFI3Yk5ZRWdiQm1jTzNGckxqRnRYK3R4b3IzUk5wMkxmTmw2ZVZoc1k1Wk5veHBkWTJ6TjM2RVBLT2hmSjNuQUtudEZYY2tDd3VJNDNVT3FJQ0htL0Qzenc4UzJaOXhmSEJXc3hiYm0vZEhOWjRtemkyWm9IM29TeHhXS1ZTN1lqWDBXK0lmUTJTUG9vR3RYUUgyR0FYUjQ4RXZDY2xFUFBRaEd5akVTdnR1dzM0TXlibWNKblV5RStLbU5yU0xnMndQYklMYURzZDdxa0d4bnozTkRGRlJvTnVPbVhScmVjdUpZRGV0Q0ZEeXVRRitucU8xcXNvWEgvdXNDRkxLZi9XRnd4YTMwaFdOSlFTQ0cveXgiLCJtYWMiOiI0MWFlMmIzYTUyMTgyYzNhOGZmZjYzMjEzMzg3YjYyOThhMzc2M2M4MzA1OGE2NGIzYjBiYTUwZDJkZWY3OGFkIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:02:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZlZHRVR3VWd0g1V01BRUljSHpsR3c9PSIsInZhbHVlIjoiZjF2TDMrTDdRSGNScmFhWllEQW92Z0NUNTd6Y2JiSnZMZ0hIZ01YUHdndDVOOWJZNGk5b0VNQ3NjTTNxK1g3SkJTZUd0ekRZQSs1S2E3SkxRVFA4c0IyWDdWTDFXU09CTHlHaUJEaGtlV3B5d29tSkh6UlRKVE93TjNyaXlGTldlOFh3MjdBaHdMSCtCbUNKUlVROTY1aE5YZHdqbFhCeVp1OStjQ1hCcXFuY0NTT1NOamdjU01kajZSdjd1OE50bkRiTHlTMEVJMkhad1ByRGZKS2czZVZMWms0dmUraURYUTlJQ2xSL09GdThVRTRtcnJzMmlCNW5EcmRzd3VVZW12diszaWRtYk42cC9yMzZhZ1J5dlA3OEhYLzlvRnRkNjlGY1RLSEd0d0pEUHg2b3VacThxRkprSnk4aUJYTkVUajFBMjduOExpM3NBaExXMFNya0VOYkEyYTdlR3ZKc0VWZlJPN3BpNmRWUkhMMGNXM2ZZd0ttQVNEOFgwQlBwQjFOVCtkNEVGTUdaYURTZnFSZzFNay9kekI5bjF3OGsrdzFPNjU1Y3FEZnFLS0FTczhzNkE1eE9rYmE1ellqVFQ0d2I3OUZTd1JDQ1ZXMi9udnFRRGNLRXhpc1NkSXg1Z3VVbjNJM3pRY2RSa3pPaSt0cEpLYVd0ZHpMcm9EL24iLCJtYWMiOiJlMzczN2VmNGNiYzQ4MDZjNmM5NDM5NDI5YjI5YzQ2NjAwNGI2MDcwMTAxZTRhZTZjY2E2ODI2NWQ5ZDYwNDExIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:02:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNSeUs5U080c3Z5dmtMV0NHVFVXYlE9PSIsInZhbHVlIjoiL0tPNU1KV2xnUG5NQ1JkZkEwalN2WmVQMDJocHlOVElhaW8zMWZubThRSFBEMGhtdGp1cHBCUGx2NjB5ZWtQdFN6UTZCQlVWS0hkRGR5UFJqUkI5d0twTjFEVDU3TWI4MWY0ZW9aVFFQWWFvZ29sd1ppcTFXcXFDYmwwejYyaUk0ekV1RFdVY2Q1SHBDanFSbHd6ajkyYmhqVEJGeU13SEpaNE1GTUZLQnFMZUtscGcrM3JVdUJxemFyaFp3S2EyTzZWbUpWUnpZdGRiVW1ydUZkY2huL2hoUnZqZCs5VkkvSHQ1RXhGODBVajl4NFI3Yk5ZRWdiQm1jTzNGckxqRnRYK3R4b3IzUk5wMkxmTmw2ZVZoc1k1Wk5veHBkWTJ6TjM2RVBLT2hmSjNuQUtudEZYY2tDd3VJNDNVT3FJQ0htL0Qzenc4UzJaOXhmSEJXc3hiYm0vZEhOWjRtemkyWm9IM29TeHhXS1ZTN1lqWDBXK0lmUTJTUG9vR3RYUUgyR0FYUjQ4RXZDY2xFUFBRaEd5akVTdnR1dzM0TXlibWNKblV5RStLbU5yU0xnMndQYklMYURzZDdxa0d4bnozTkRGRlJvTnVPbVhScmVjdUpZRGV0Q0ZEeXVRRitucU8xcXNvWEgvdXNDRkxLZi9XRnd4YTMwaFdOSlFTQ0cveXgiLCJtYWMiOiI0MWFlMmIzYTUyMTgyYzNhOGZmZjYzMjEzMzg3YjYyOThhMzc2M2M4MzA1OGE2NGIzYjBiYTUwZDJkZWY3OGFkIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:02:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053636831\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1052854878 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052854878\", {\"maxDepth\":0})</script>\n"}}