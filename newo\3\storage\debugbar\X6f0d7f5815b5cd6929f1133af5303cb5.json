{"__meta": {"id": "X6f0d7f5815b5cd6929f1133af5303cb5", "datetime": "2025-06-16 05:04:05", "utime": **********.454137, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.670823, "end": **********.454186, "duration": 1.***************, "duration_str": "1.78s", "measures": [{"label": "Booting", "start": **********.670823, "relative_start": 0, "end": **********.16787, "relative_end": **********.16787, "duration": 1.***************, "duration_str": "1.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.167951, "relative_start": 1.****************, "end": **********.454192, "relative_end": 5.9604644775390625e-06, "duration": 0.***************, "duration_str": "286ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019450000000000002, "accumulated_duration_str": "19.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.30826, "duration": 0.016030000000000003, "duration_str": "16.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.416}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3542411, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.416, "width_percent": 7.147}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.416501, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 89.563, "width_percent": 10.437}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwt%7C0%7C1982; _clsk=1y3ys69%7C1750050242430%7C3%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjR1bWh3RG4wZlV5QThtczZuZkQ5Smc9PSIsInZhbHVlIjoiSUFKcDA0V241dHdZVkhwZDJwTE05UEJDK1NNR3Z1dm80N0NTa2M2QVlGVlp6eHIzeFplYlFFYzhSNDJEMENWM3lWNnE3OXJrWWUyV2lldDlFUTBuTXBpaXhnYytHMnJRWkluUWJaeXdCNWN4SWxVQkhUTnROaEtOTTV4OTJOYmMybGFDbEEvWHBrZGZpM2ZERTR0UW9VY2ZSN01vZ3BraC9ZR2tiVGRIdHFNNW1GcXdjQlk0VHFVT2JpUXhWeFU4VUZwbnY2am5yN0ZHaUJmelpzRTZDbGJzWTJYMVFGaGZxU2xGQ3l1YkdXdUliUXJJckowdWtwcS80Y0FHaEhIRlYvSmhKNUpoQkZOSzE0azNlaWwzNVJRYlFFcEtGVTZucS9TbDRucGJBc25Dbno0d3hDMEZmUlpYamphdko1VjVZb29oSGwycm51TURlekllaTZkMm8yMkJiMC9Mc2c5b2lycE85dCtRdlZPT0kwdTI4bVhBV0FHQzJOZXdiTGtVRE5LU2xDVmJid3M0TmhKWDcwK2hBV0ZwWmd5Z282WnhydEtRS3FjVHBkTUF1QUcvWXFyWnlmU3RHcSswY1VhcDBaY3cxOCs3Q2VxWmo0Mk9BbmlWckM3ak1rcjhoM3BPMEQvYmowQWFiS2JPTExyNUpaVi9zTmtnSHFGclVuOWoiLCJtYWMiOiI0MzFhZGI1MmQwODZlMTliZDU3ZGY4N2FiNjMxYTJmOTU0MzAxMjZiYjM5NjA5NjhhNDFlMWE0MjIyYTlhZmQ0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFQeHhtNVVIdUVwcitWVCtMU002aFE9PSIsInZhbHVlIjoiRS9CSWNKT3U5d1ZVRS9DZ1pMR1daa2x6Um85aDJJQWhBTFZlK2ZvQjlKc3pScDRuSEZvVVFTbVVSQ2g2L1Z1RjFLSnNLZ2V5em9iSWxUaS8zWnlISTd2QVQrM3BsQnZVMGtxOUVXekt4cnR5S0dOOXJqSFhDRndlSW12QjFObGV2cjFFVWdRcDQ2ZlkvUy9sOWFaYThwQk9YUDB2eVRnbVM1VkNFdmJwYzNVdG04NHJGdXYrTlRXWitxNkhZeHBHcDRaQytYbktSeUtWVC80OVRoZlV2TTY1aUZkSGtnUUpWbTRRbFBZMmhhNHFSWXZnOEdrNEJzQXgxVTdvS1BhbzQ1UTl3a1I3MENjaEJNUkR0dWRheEw4NTB0UFR1V3dvV0doTFZNSTZoUVJsQ1FYKzkwQ0FtQTBiTWJYSTg5SE5OdFVlK3MwRmFmZ0lHS3pWV0VmS3pESDJmVmduTElsZEh6S3ZwVkMxQTUyNmJYZVo3Z2RHKzVLQURzTC9kYWN6ZnZXdCtBN1Y3S0wySmJnbmNrcjRuVFM0eXJCdGdLSUpLbm51cGIxWGwzcVFhYzAzaU5CNDQwTmtlZ0tLRk5Gb01BWUNYcktPSklzT1NyR0trWGd0Wm1Bb21pVmdpV2l2TzBuenhWcWhBRERzMlkrOVlJL1hjb3JBbnlaa20xVEgiLCJtYWMiOiJjZGMzMzRmZGNlM2FjMjUyYmIzMzY1NTNmMzlhYzUzYTQ5N2ExNTExZmRkMjlhZWJiNzMxNzE4MGI1MDgxM2JjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1485202553 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">F52gGvLLY2TuuySQOODF6RGytIRL64hrZr70fxyL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1485202553\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1277593451 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 05:04:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpjVWIwZ0psbGQwNjkzQVFqd3FweEE9PSIsInZhbHVlIjoib1Y3WDZkSzNFNk9Cc29BSGhUUHQ4T2NWV0VHZ3pOVDdoWFczZFdDbTZPVk8vRHh4UjJUdXpnSzhRa1ZLYkZtMjIxU2pjaDFGLzllSVRDdnRJWWtOdnhianNzUHZxVkRZbmNxNVlFRlExaTRTTTlhSXJSRU1zLzN2alh5UmdDSEZGVFdudVcwNmFjMHdsOUh1eDRQL1ZZcGhFTHhxMGdnTEhoYUhJVmdsdjY0eUNibXlNb0ZBVmxPUjZyL3VXd2RVT09NZk9aTGhqTENqVTNWS2tuMTJmdElxM2tSeW5SZG9pVU50OUM4dzZLNXdBMWdJQjRuN3RETVpRWHpSVk5kV1dQbnVQaUdyOVhqZzJUbEhWaWF0Q2x1akpuRXZ0bUk1RkcrTTFEUVhyTTVHdVhOY1F5TmpibzkwVmUwZVI2UXNpaFlGQjgwZTUxeUU1NjV5SnFPQ01TR1ExQk5VMXFxZWFhQmhqNG01Y2I2K096NDNzZzB4THl5ZllZdEtPalVmOW9yTmE2eUxQMmVvTGhta25wSDBxQXZKVkM4UkUzZS9zenRyNFZBQmVEcnlmMmd6Tjl3TmgzNzc2T3ZYVG9vY1NmRm0ralhMYmlhK2xzdEhucS9nSkFJSWpudkVLTHliL2lGUmVpR0VCYmtBYnZPOWQ1dStnazY1anpXajExYkMiLCJtYWMiOiI4OTA2YjgwNTQ4NjlkNzQ2NTY4NDc3MDM4NTg5MjJkZDllZDg3YTA3ZjVhNjNiZGIwNmM4NzVlZTM4NjM1ZTMyIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im1IZTRsUUs3T1l6YnFpc0p4Q3ZHclE9PSIsInZhbHVlIjoia21WdHBkSzdwY1J4ajNiTXkvUjZWRENTWXdXbDNlMHp2YXNnTnpZVDRyeEhhNVY4Zk1qUVlNcGZoZXYydHQ4cXFuQUE0N1lXKzEvS0ExS1pNSkpSQjVwK0pjR05SMXM3N2hoWTdEUzVZNVU4K0o0RTN1cXBNb0Q2SjJCaU9nQjhiaEU5d0ZnUzZtcHhnZ2ZtQ2E2amFxQkRQL2RuWTN4c2ZpQ0pwVFRXWUtMOUFBYUhXZVk0NDBWSVVOT0pQalMvVnA0d04wUFFxTnd1NU56RjJJN3Q4Zmpwc1IxRlQwY3dzU0VUdWR4d0pGa293VTJXQ0pGL1I5L2VIcVI3OXJEamxxTVNpOWN4TXRYOUNvMytkVEJna2xPa3NvUVl0QmlISG5PeGJkcDVwbnpLNEpkd3RMNTkwRlc1dFBnUGt4VHRkblczN3cwVFE1Yk8yeVJRaWFPOXRUS1k0L0ZnaENsb0libWxCL3hJSUpYMlIvYVVCZklKNzBJVGNVOS9pODBGYkVnaEExWE53TWFhU0hoQmwwUVFTU0s2WVZHdXRjQnQ1eUE1SmZhZU1DdGRab3FjdUI4ak8wREs2MmRKNS9FWmN4WEZRMnJudG9VamxMQXJvK0oyV213RHUySDJCTW4yNmkyZktvUlRXMlBFTTZ4RTlJTFZjNzNUd2NBaGtzSmciLCJtYWMiOiI4NjcyMDRlNmE3MjEwMDNiZDE1NmVmMjc2ZDE3YzczZDZlOTFlNDRkMmFmODIwZTBmMzlhMjc0MGQ5MjdmOTIyIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpjVWIwZ0psbGQwNjkzQVFqd3FweEE9PSIsInZhbHVlIjoib1Y3WDZkSzNFNk9Cc29BSGhUUHQ4T2NWV0VHZ3pOVDdoWFczZFdDbTZPVk8vRHh4UjJUdXpnSzhRa1ZLYkZtMjIxU2pjaDFGLzllSVRDdnRJWWtOdnhianNzUHZxVkRZbmNxNVlFRlExaTRTTTlhSXJSRU1zLzN2alh5UmdDSEZGVFdudVcwNmFjMHdsOUh1eDRQL1ZZcGhFTHhxMGdnTEhoYUhJVmdsdjY0eUNibXlNb0ZBVmxPUjZyL3VXd2RVT09NZk9aTGhqTENqVTNWS2tuMTJmdElxM2tSeW5SZG9pVU50OUM4dzZLNXdBMWdJQjRuN3RETVpRWHpSVk5kV1dQbnVQaUdyOVhqZzJUbEhWaWF0Q2x1akpuRXZ0bUk1RkcrTTFEUVhyTTVHdVhOY1F5TmpibzkwVmUwZVI2UXNpaFlGQjgwZTUxeUU1NjV5SnFPQ01TR1ExQk5VMXFxZWFhQmhqNG01Y2I2K096NDNzZzB4THl5ZllZdEtPalVmOW9yTmE2eUxQMmVvTGhta25wSDBxQXZKVkM4UkUzZS9zenRyNFZBQmVEcnlmMmd6Tjl3TmgzNzc2T3ZYVG9vY1NmRm0ralhMYmlhK2xzdEhucS9nSkFJSWpudkVLTHliL2lGUmVpR0VCYmtBYnZPOWQ1dStnazY1anpXajExYkMiLCJtYWMiOiI4OTA2YjgwNTQ4NjlkNzQ2NTY4NDc3MDM4NTg5MjJkZDllZDg3YTA3ZjVhNjNiZGIwNmM4NzVlZTM4NjM1ZTMyIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im1IZTRsUUs3T1l6YnFpc0p4Q3ZHclE9PSIsInZhbHVlIjoia21WdHBkSzdwY1J4ajNiTXkvUjZWRENTWXdXbDNlMHp2YXNnTnpZVDRyeEhhNVY4Zk1qUVlNcGZoZXYydHQ4cXFuQUE0N1lXKzEvS0ExS1pNSkpSQjVwK0pjR05SMXM3N2hoWTdEUzVZNVU4K0o0RTN1cXBNb0Q2SjJCaU9nQjhiaEU5d0ZnUzZtcHhnZ2ZtQ2E2amFxQkRQL2RuWTN4c2ZpQ0pwVFRXWUtMOUFBYUhXZVk0NDBWSVVOT0pQalMvVnA0d04wUFFxTnd1NU56RjJJN3Q4Zmpwc1IxRlQwY3dzU0VUdWR4d0pGa293VTJXQ0pGL1I5L2VIcVI3OXJEamxxTVNpOWN4TXRYOUNvMytkVEJna2xPa3NvUVl0QmlISG5PeGJkcDVwbnpLNEpkd3RMNTkwRlc1dFBnUGt4VHRkblczN3cwVFE1Yk8yeVJRaWFPOXRUS1k0L0ZnaENsb0libWxCL3hJSUpYMlIvYVVCZklKNzBJVGNVOS9pODBGYkVnaEExWE53TWFhU0hoQmwwUVFTU0s2WVZHdXRjQnQ1eUE1SmZhZU1DdGRab3FjdUI4ak8wREs2MmRKNS9FWmN4WEZRMnJudG9VamxMQXJvK0oyV213RHUySDJCTW4yNmkyZktvUlRXMlBFTTZ4RTlJTFZjNzNUd2NBaGtzSmciLCJtYWMiOiI4NjcyMDRlNmE3MjEwMDNiZDE1NmVmMjc2ZDE3YzczZDZlOTFlNDRkMmFmODIwZTBmMzlhMjc0MGQ5MjdmOTIyIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277593451\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1082823439 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082823439\", {\"maxDepth\":0})</script>\n"}}