{"__meta": {"id": "X741fdcde1caf7c86d6592812868ecf2c", "datetime": "2025-06-16 05:04:12", "utime": **********.123902, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750050250.43169, "end": **********.12394, "duration": 1.6922500133514404, "duration_str": "1.69s", "measures": [{"label": "Booting", "start": 1750050250.43169, "relative_start": 0, "end": 1750050251.929045, "relative_end": 1750050251.929045, "duration": 1.4973549842834473, "duration_str": "1.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750050251.929067, "relative_start": 1.4973769187927246, "end": **********.123944, "relative_end": 4.0531158447265625e-06, "duration": 0.19487714767456055, "duration_str": "195ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43905720, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.018940000000000002, "accumulated_duration_str": "18.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0358388, "duration": 0.01759, "duration_str": "17.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.872}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.066704, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 92.872, "width_percent": 7.128}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1202623139 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1202623139\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1093681341 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1093681341\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1468222399 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1468222399\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwt%7C0%7C1982; _clsk=1y3ys69%7C1750050242430%7C3%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJ4RDlWWWNROGNDZWdBOUtCbDBpZVE9PSIsInZhbHVlIjoiSldOaTVhRllPajJjTnZqSjlPTHRBUVZoZk10RjhXUGMwcTdVMCs0T1dvano3R09EOE9weDNMRkxQR1YxS1VlTHd5Y3lKR2dsNzA1QmZLcE9EbVNqWk5JOFBsTklCdEhoVzB1MEF0S0F5dDFXKzUreGNsQkpUZGtqcnRQTlVMeklWeHlqOWxEZGY4NnhQdW0xb1dWZytYeW5iR3NGbHZPbGxXYWRGSGNnOU9nQXM4c0lvYmNodkkxcGRDSWMvNUd6NTVxTC9hYUpVeUg5cm5SOTdhYVQ4U09aVmVBcXF3Z3FhZ1V6TmdGdFdaaEhnK082K0pyUUI4ZkZ0WVhUMVBpNjRDelhubXJja1FTc0dNY0djUEdkaU56VFI1MUlVWEN1VXdHUTVVbGtVbFUyUC8yWjVaYmhLcHNqSTdoak1keTdUODNsUXU2UDVUdmY0dE13bmpxakVvOEpkWVNySFlKa3NpeVNITXhRS3NwYzV6KzhpZ1BsZGZIYzV2eXdndE1Nb2dUVHMzNHp3UDdqY01DRHJ5eStZR1ZaTzY0NFc5UkRkOUF3L3J5ekxxMUxja0IzdDAvYVFQUDdrTUFueEd3c2RtR0UzdW9nMkZ1ZnhJZEVSSTJzc1g2azY3ajRpakpTVDZtV1JpSHcrTFdra3luMUlZRHRCVTVKTFEvbTRWNFoiLCJtYWMiOiJkMTllM2M5ZTA5YTM5ZGJhYzA1NDhjNDllZWEzMTZmZjA4N2JlMGM5ZGRmYWJjMTBiMDQzNGE5NmNhY2E2YWQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkttTkhVak5kL0ZOQk5UK1lsL3dUeFE9PSIsInZhbHVlIjoidFFzWDVWWDV6SXFBcU96aVlLd0xNaWE3c0tUVC9mTlk4MytQNU8zMUMxWjhuN2V0QkhmcE0wTlByVTBrSEhWL3hybUYrV3dMWHFwdkNZR3B3WjJuTkpOUDhUcWZPelEvUGhVajlRdHlyekF6R1Fwa3lqaXNwWTZYVEtpQXlYWHBIY095amR5aFBpWUd4aUd0VExJN1M3NFZHQ0ZWM2thR1E0ajVja3lJelFPL1FPVGpuaG9ZWUVXMzJ4ZkxEMzY4VmNibjBuWHdUUlRJaEZTYmVYeEljeVBNTWozYW81UTJxTDlyZUV0WVJscGxKNURZS0NsM2xLRzU5V3ZlUEJjMHkzdnEvNzJXRXRnMVlFVHh1elNOTnM1SitGb0hBMlArS0E0VjVXQUN5WE1PTDhMdHdVMktqTWt6SnhYaVFoZWloSXJqV3Q5UmJJNTExZ1JQSnJhOGZCNUpMTFFDc0RvZDRaTkNYWXBuaXhWa3FNbStOWlgrQzl3TmFOaytNTkRQRlNKRUVjT2JkY1RVZ2JCdm5HOUdQUndFSUlPcS9qZURnd29uY291VUdsdlhBTll6SEY2ZFQwaHpLbllGblB4SkowVzNzcVFoR3ZROEdYQUltTTI1UTNTT1h5ekxGeFlQUThVVWc0N1Zmamx0RE5JamFhWEV5T0RNTjhEK2lqZGsiLCJtYWMiOiJjNDAxYmQzNzc2M2IzMTEyMzI5ZGZhZjJjNGViMmZiODVjMTgxNGRjNzRjZTZmZDA5Yjg4MjhlNDk5MzIzOTI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-395613165 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">F52gGvLLY2TuuySQOODF6RGytIRL64hrZr70fxyL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-395613165\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 05:04:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkM2Sy9TU0JKMHhZckJGOXp6MHo3Y0E9PSIsInZhbHVlIjoieDFnK1l4MzF0Z0IrcG5pMjBKWHpjdTk3ZkVTa2w1b0tMQ1Q3OE9zRHYybm05TTJoYnhxMUlNZEtrcEpLR3BLck9paHZ1ZlJIVWJsQzhqSTZrbW1QT1ZmNnJ4K21nY1RFaUhXNnVraDRucDdwSWJsaHQ5eWI4QTh1Y0kwdXRxb3NYUEVvZFR0WWF1RXY5WHBvRHphdTZwamhrSlhWQzE4MjU2ME1WcHFzY0kzWDlXYnlyZ3B6ZFZ3ejYydWVvTWh2Z1Nqa1FGN1ZTQWZHRllNTUxkMC9FSHdrS3NlejVaQ01aTitSRnVrNjdTazB5R3RrQVJySkZSWnRNQzMxd25pRC9VbHdVRERhcTZzM21EcWYxZXo3Qk9qcyswN0hNSkd1NW14NmdBbTV6aGFadCtiRTVib0NvR3ROY2xvWGpET1c0MFJ3MmZxbGZaKzNUMytvWkd1RmdKaWJXNUdta2NmcnAvSzFVbTlHKytEOGZWSnVsVmZYL2h0R0ZRNkx6bHlBMWdiYkFNendOa3RVcXRQcTVzMWJGWXlVOHNPQ2h6ZGdUMnRoQWU3cVVCWGx5bHFDeXBla3BXbFZwaEd4U3p4dHhlQTlKR0srYlJVMEM3OWx0bnBXVFdFTVZ0a2s4b1pWZXVhYmswMjRJQVdub3JIY0RheEZubXN3djRUZnVuRWYiLCJtYWMiOiJiYmI1OTM5NDU5NmM3MzlhMWQwZjJiNmZjMTliNzdiMGUwOTY5MWU4ZTA0OTRjODIyZDgxMWQ2OTlhYWE5YmNlIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ims2Qk4ra0dVMjNLenFCT2lycU9TSkE9PSIsInZhbHVlIjoieFdkYVNIQ3kvanhLUFNUdDN5ek5uZG56L01XWEw3U3NvUFBhdmNQVDBsMEQ3UGtROUFpTTYyK0EzakpucElzUEdTL1pWc3g1TkYxVk1HeS91MzNMcVMyZFdkRVlEcWtvWmZlVnUvSGxoQjh1MzZGblNIdmFWQklxRWlJTGw3QWpqTlM4VDgxeDFwUVZMWjJMRVhXS0xrc2QyY0VDTk5NOEIvVGM5ZVh4UVppVDZrdWY1aFpaeGVZM2huTlJhcVRJZ0J3bG1lT1l5Z0ZqdFg2VXFwa1NFcHEwMUdnaXppT2lGRnhDd0NNZElQQmF6NnAzS0UzblJnR1FHbE5uSkNGYTMrNElsbDNOVkVIeVY1d2lxZ1RUd0lLc2tCdnZodGNOS1dzc2NtNmd6ZXYvOE5PZWlOb1pBeE1XV3Btem5NVGMvdllyTTRZOWFxSkY0N1B2bm5Nd0h1bkF5YVZ2ck1vaitsV016dFhGMUExWlQ1TEtEeXFZVTVtYnd4NGczR2hlV2hWSnYyWmJ0QnlRV0ZKbFhnZ0FqT1JvSTh5UUpvRVVkN1ByL25qdGlWMDJwUUFtWGlzeEJKdXg4Z3J6M0NBaUU3bk90R3hTcnFDNnhZQnpWdVFHR2g1UmV4QS85THExRTNBRzZ4NjE2clhGQmhIcEFNYk9zUFpTKzdmN2hvVFUiLCJtYWMiOiIwOWE1MmU2ZjI3NjczYmNmMGIwYjE4N2RmMGUzNGM5MTAxZDhmMmYwMjIwYTMxMDdjN2I0Mzk3ZjJmZGZjODJhIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:04:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkM2Sy9TU0JKMHhZckJGOXp6MHo3Y0E9PSIsInZhbHVlIjoieDFnK1l4MzF0Z0IrcG5pMjBKWHpjdTk3ZkVTa2w1b0tMQ1Q3OE9zRHYybm05TTJoYnhxMUlNZEtrcEpLR3BLck9paHZ1ZlJIVWJsQzhqSTZrbW1QT1ZmNnJ4K21nY1RFaUhXNnVraDRucDdwSWJsaHQ5eWI4QTh1Y0kwdXRxb3NYUEVvZFR0WWF1RXY5WHBvRHphdTZwamhrSlhWQzE4MjU2ME1WcHFzY0kzWDlXYnlyZ3B6ZFZ3ejYydWVvTWh2Z1Nqa1FGN1ZTQWZHRllNTUxkMC9FSHdrS3NlejVaQ01aTitSRnVrNjdTazB5R3RrQVJySkZSWnRNQzMxd25pRC9VbHdVRERhcTZzM21EcWYxZXo3Qk9qcyswN0hNSkd1NW14NmdBbTV6aGFadCtiRTVib0NvR3ROY2xvWGpET1c0MFJ3MmZxbGZaKzNUMytvWkd1RmdKaWJXNUdta2NmcnAvSzFVbTlHKytEOGZWSnVsVmZYL2h0R0ZRNkx6bHlBMWdiYkFNendOa3RVcXRQcTVzMWJGWXlVOHNPQ2h6ZGdUMnRoQWU3cVVCWGx5bHFDeXBla3BXbFZwaEd4U3p4dHhlQTlKR0srYlJVMEM3OWx0bnBXVFdFTVZ0a2s4b1pWZXVhYmswMjRJQVdub3JIY0RheEZubXN3djRUZnVuRWYiLCJtYWMiOiJiYmI1OTM5NDU5NmM3MzlhMWQwZjJiNmZjMTliNzdiMGUwOTY5MWU4ZTA0OTRjODIyZDgxMWQ2OTlhYWE5YmNlIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ims2Qk4ra0dVMjNLenFCT2lycU9TSkE9PSIsInZhbHVlIjoieFdkYVNIQ3kvanhLUFNUdDN5ek5uZG56L01XWEw3U3NvUFBhdmNQVDBsMEQ3UGtROUFpTTYyK0EzakpucElzUEdTL1pWc3g1TkYxVk1HeS91MzNMcVMyZFdkRVlEcWtvWmZlVnUvSGxoQjh1MzZGblNIdmFWQklxRWlJTGw3QWpqTlM4VDgxeDFwUVZMWjJMRVhXS0xrc2QyY0VDTk5NOEIvVGM5ZVh4UVppVDZrdWY1aFpaeGVZM2huTlJhcVRJZ0J3bG1lT1l5Z0ZqdFg2VXFwa1NFcHEwMUdnaXppT2lGRnhDd0NNZElQQmF6NnAzS0UzblJnR1FHbE5uSkNGYTMrNElsbDNOVkVIeVY1d2lxZ1RUd0lLc2tCdnZodGNOS1dzc2NtNmd6ZXYvOE5PZWlOb1pBeE1XV3Btem5NVGMvdllyTTRZOWFxSkY0N1B2bm5Nd0h1bkF5YVZ2ck1vaitsV016dFhGMUExWlQ1TEtEeXFZVTVtYnd4NGczR2hlV2hWSnYyWmJ0QnlRV0ZKbFhnZ0FqT1JvSTh5UUpvRVVkN1ByL25qdGlWMDJwUUFtWGlzeEJKdXg4Z3J6M0NBaUU3bk90R3hTcnFDNnhZQnpWdVFHR2g1UmV4QS85THExRTNBRzZ4NjE2clhGQmhIcEFNYk9zUFpTKzdmN2hvVFUiLCJtYWMiOiIwOWE1MmU2ZjI3NjczYmNmMGIwYjE4N2RmMGUzNGM5MTAxZDhmMmYwMjIwYTMxMDdjN2I0Mzk3ZjJmZGZjODJhIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:04:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}