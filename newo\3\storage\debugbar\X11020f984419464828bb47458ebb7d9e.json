{"__meta": {"id": "X11020f984419464828bb47458ebb7d9e", "datetime": "2025-06-16 05:02:48", "utime": **********.385811, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750050166.675245, "end": **********.385857, "duration": 1.7106120586395264, "duration_str": "1.71s", "measures": [{"label": "Booting", "start": 1750050166.675245, "relative_start": 0, "end": **********.076809, "relative_end": **********.076809, "duration": 1.4015638828277588, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.076836, "relative_start": 1.4015910625457764, "end": **********.385862, "relative_end": 5.0067901611328125e-06, "duration": 0.30902600288391113, "duration_str": "309ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45206848, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03308, "accumulated_duration_str": "33.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.194371, "duration": 0.02708, "duration_str": "27.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 81.862}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.25389, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 81.862, "width_percent": 5.381}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.32727, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 87.243, "width_percent": 6.499}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.354163, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.742, "width_percent": 6.258}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-39774384 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-39774384\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1821011861 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1821011861\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1232825217 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232825217\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2018829602 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwt%7C0%7C1982; _clsk=1y3ys69%7C1750050149178%7C1%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Img0MDU1cGwvZ2lzMkx3bmFsYUR3ZFE9PSIsInZhbHVlIjoiYzJ5L0FFclBTYm5UT01mREN0b1VTZ1QxenJNM3YrcjdjbUQybTlkYTlpbmdSNzFvMHFwbUs0YVBpV24wV3BNT3ZXQi9kUitwem1yYys4QzNtUFp5ZUExa014WHBpNE9ycnJtQWc4YUI1TDVwdlJMU0tzbmNzSnRIbDFhYTdJUG5aTEtkNk9OY1drdWJ2NGkwdFBsRkZ1MW43WWZpUkZMU1FmdVU4eEN5d3ZqNDBjYUxhSnZDN1E0QVpteVNOZlZ4alNhSE5UL05tT29tYzFiVGcvU0lCdHRtYkp5cUhNd3VqOS9qcVNIVldCcitsRlFSMWh4VEVMenRwZi9rZUIzQ2xqaHFIVncrOGJkYmFkSWpUaThxZ1pLazRseklOck95NEptSlA2WW5GYkFSSkNFZGNuN1JuY2dCTlFOTEpGcmJUR1U0c3Jja01SZHFPMStBeXFNVjVLZ3c2Y0orMDd2MnpiNlNSTEU0bEFvc0plUFpGS2F5MFYrVUhKZStzNWpHUUFsMUYvQTRhRTZZZ1pXaWtBOWdkS0U5ZWNMN2JLSnNrVUpEdWRUWDFHUjh5MXhicDBiTEJqcTc5bzlZQ0hrNkI4ZHk1K0hwdjRRZU9Fb3ZFNDBsaEVJUnhPeWdDdWZWQWxrWnZBZVZRdG12cHcvbGVzOGp1LzhiS2hUSkRkNXAiLCJtYWMiOiJjMDdiMzVmODczZjM3OTczNDI3NjdjMGFlNDQ3NTg3MDdjNGE1NjA2MzY0NDVhNGI0NjhhMWMyZTE3NWEzMGRlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imw0MG5SN0REREwrcys0SWIrcTJKTnc9PSIsInZhbHVlIjoiWHlBczUyeHlLQ2ZHSGMrQzVsTysvUmVxZGxpWisyWTZ1TGNDVGo4VkRQbjJaRi8zWHV3OXVDUk9WZ0Y2UDA4eFNrblBCQTJEU2hzK1Y3NGk3eElEa2FYSkg0TUZXWWx6OVZVYUw3L2tkUDVaUys4ZEdFV3RCMzA2bjhCeDBLKytwTEp6TUV0aHRLQ2ZMYVRIcDhjd3lTQ3Uyb0xxME9kWnRwM3FWM2FsK3FUL1ozS3hoT3hpeGlKM1JrMEZ0VVJLcTZDV1owek9HNitZeGVPUU9qcHRLQXFEd3FQQmJZam1HRE9SVWJ0TXdYUis3L3JWV00zU3pNcnQ4enlUNWlJNVpnSVl5MGlDbkl1SGFUQm9MTS8yNnBWck1Sb2dFcUx4TUtFKzJubmxldEc0VzZqTmtXWnhFcDJXUTBackpzdUxNak5UQlJESEE2ZTl1TFg5UE43Q3lkMUU4R0hXejIyZ3FsTUhTVTYrdzVRR3lVWXV0Rk9PamdkQTNISlFQTmR2WDV6aEI0NXgyeU5OSFo3RkpTRjFlbkJjMjhHL081Y0FXRGpLelVGeUM3eVJDTzJVbVhPSDBGdkttM2oxdWpkVzZlcXkyTGFIVGUxcHg0TWlKN3gzUXNheXpYV2g5N1RmK2FyMjBHenhiSjBVbEk1L0V3UTRRMGpVajF1cGVDb1EiLCJtYWMiOiJlODNlZDc4ZWJjODAxMDUzMmVhNWFkYzQ5ZjhiMWQ5MWU0YzJiNGU1ZmNmYzVkNDI3MDkyYzkyN2JhNmI2YzgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2018829602\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1147373791 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">F52gGvLLY2TuuySQOODF6RGytIRL64hrZr70fxyL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1147373791\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1905792444 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 05:02:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijc5QUphUHdOSnNrR0VLQ2k0dXFabXc9PSIsInZhbHVlIjoieStUcWNaOTAya3pmTit5SDB2RHVRdzhTeWFjR2Fsd0dIY1BOQjBrdXRJdG9xNTdpbGpyWnEzRUw0S2kzc2d3MmZoTGF3VnRhbjBud2Myc1oxUEdET1BGNGZPZTVMbE4rMm1UekkyQWxma2ZZcGg4ZmR6ZWZiRnJXeTZFMkpCK0dLcEk2SFdCQ2s1QU9BYmxIU0JHTHFESGFqVDdZSWdTa3U5RVJzMjF5M2VlNnd4YTZOZ2FTK2JwOVNCbUg0SkRINDBuOEo5amMraWlIbzMrQThYeHlZZm44WUNneENGZ2N2WDJjRVlBK3hNS2JoeXovNWVpN0pZTSsxR2wxVlF0M3dlWGJmVW12WVhMdkNpTlhyVjZWTDh0eDVnWWlYV0ZpQ3NmV0FwMTRMRU5NclRuaTl2Sll2M1pQVnQ5ZDNpdDZ1c0wybUh4ZmQrTHlPTXNiNCtnV2dDeVRBSmowRXZXZlhENXJoOFljb1NscTZaekYwTWhBdXBEZEVkaXlkTkFoVGU5aTVVZWtaSjJOUTZ5WTI3MEQ5aHF0NkRVOVo2aklRMWYxZGFFL2ZYUE84MTJ2NGlQVEk0cWZXV0FLdnhIRlNoNGJwNmJ6MXgzU2dxTFQwbE9UMHR6eEMxbC83M2VPUldXQlFQY0d2dHhUTVFqMlZhRVNHUVB3RUcyUkxHeWQiLCJtYWMiOiIxY2NhYWZlZmQwMWQ0NTI3ZDI0ZGNjMTY1OTI1Y2QwZTgxYTExMTc1ZDQ2N2FlNTlkZTE3MjMwMzAzN2RiYWZmIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:02:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNIZnd2a0ZORVhyUWdmdmp3b2xOUHc9PSIsInZhbHVlIjoib2hrZXR1QS9JRkErTlNwK2FtMjBVdUFaamxXa09CSDNvSmduaGtqUkduRnJjQ1ZjUDdkc25QWkhxRzU5dWJHOVdUK0czZUNlUFgxZjVlUW9SckFhTkxRanhzejk5VTJRZmVIK003aUtkUE1ZUldqcjVpZE5weFNkZEwvMXNqMnVUdnM1ZFU0aFI0MHA3WDBZckMrNmtPRkNMd1MzbDV3ZU5EeFZ2YWp6R1k5aEtITXZoOUxSeVYrSC9FYzIveDc2Wm5Rbmd2eVNJd1pxZ2R0UDZGZnNNWlRBOURqTHFORHdQOEVQS1ZSYkVoMlRsZHRJZ1k4WTVDdFd4MzljV0VWNXlDenNySkpMZ2hUMUtnQ2VYUC90NmRHUWt5Nko2MjExcFFHOEFCNGp1bTVvclB3NktEUVRqOStNN2tqRlZBZ0FWWkt0eVZBdHVveWhKZGlqejYwWnd6bUV2b0g1OTBPL2c5WW5FdG41WlFVQm9TWFMvWFVjVHVVRDVMZ0lsaFpUakFva1h4bzNVaHdjaXgxeUtJUzhrQVNHT3JKdTBZb1ZIWTFkWUdMeTBRTTZHd1BQY1JCaGhtamw5c1pyWHA1cnlwSWdUMXYyZHVuazJybU1RYmp0QUtWd1Q5Y05VdmJ4N3JrRmF0NTBxVmVXdkh5QjU2K0FScEQ2aHRROHpNc2siLCJtYWMiOiIyZWExNDY0ZDhiMDljN2QyNDczNjJlNzY5OTZiODNhOGYxMWQzYzQ0NzdiNThhYmNjNWM5NTlmM2IwM2VhMjQ4IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 07:02:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijc5QUphUHdOSnNrR0VLQ2k0dXFabXc9PSIsInZhbHVlIjoieStUcWNaOTAya3pmTit5SDB2RHVRdzhTeWFjR2Fsd0dIY1BOQjBrdXRJdG9xNTdpbGpyWnEzRUw0S2kzc2d3MmZoTGF3VnRhbjBud2Myc1oxUEdET1BGNGZPZTVMbE4rMm1UekkyQWxma2ZZcGg4ZmR6ZWZiRnJXeTZFMkpCK0dLcEk2SFdCQ2s1QU9BYmxIU0JHTHFESGFqVDdZSWdTa3U5RVJzMjF5M2VlNnd4YTZOZ2FTK2JwOVNCbUg0SkRINDBuOEo5amMraWlIbzMrQThYeHlZZm44WUNneENGZ2N2WDJjRVlBK3hNS2JoeXovNWVpN0pZTSsxR2wxVlF0M3dlWGJmVW12WVhMdkNpTlhyVjZWTDh0eDVnWWlYV0ZpQ3NmV0FwMTRMRU5NclRuaTl2Sll2M1pQVnQ5ZDNpdDZ1c0wybUh4ZmQrTHlPTXNiNCtnV2dDeVRBSmowRXZXZlhENXJoOFljb1NscTZaekYwTWhBdXBEZEVkaXlkTkFoVGU5aTVVZWtaSjJOUTZ5WTI3MEQ5aHF0NkRVOVo2aklRMWYxZGFFL2ZYUE84MTJ2NGlQVEk0cWZXV0FLdnhIRlNoNGJwNmJ6MXgzU2dxTFQwbE9UMHR6eEMxbC83M2VPUldXQlFQY0d2dHhUTVFqMlZhRVNHUVB3RUcyUkxHeWQiLCJtYWMiOiIxY2NhYWZlZmQwMWQ0NTI3ZDI0ZGNjMTY1OTI1Y2QwZTgxYTExMTc1ZDQ2N2FlNTlkZTE3MjMwMzAzN2RiYWZmIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:02:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNIZnd2a0ZORVhyUWdmdmp3b2xOUHc9PSIsInZhbHVlIjoib2hrZXR1QS9JRkErTlNwK2FtMjBVdUFaamxXa09CSDNvSmduaGtqUkduRnJjQ1ZjUDdkc25QWkhxRzU5dWJHOVdUK0czZUNlUFgxZjVlUW9SckFhTkxRanhzejk5VTJRZmVIK003aUtkUE1ZUldqcjVpZE5weFNkZEwvMXNqMnVUdnM1ZFU0aFI0MHA3WDBZckMrNmtPRkNMd1MzbDV3ZU5EeFZ2YWp6R1k5aEtITXZoOUxSeVYrSC9FYzIveDc2Wm5Rbmd2eVNJd1pxZ2R0UDZGZnNNWlRBOURqTHFORHdQOEVQS1ZSYkVoMlRsZHRJZ1k4WTVDdFd4MzljV0VWNXlDenNySkpMZ2hUMUtnQ2VYUC90NmRHUWt5Nko2MjExcFFHOEFCNGp1bTVvclB3NktEUVRqOStNN2tqRlZBZ0FWWkt0eVZBdHVveWhKZGlqejYwWnd6bUV2b0g1OTBPL2c5WW5FdG41WlFVQm9TWFMvWFVjVHVVRDVMZ0lsaFpUakFva1h4bzNVaHdjaXgxeUtJUzhrQVNHT3JKdTBZb1ZIWTFkWUdMeTBRTTZHd1BQY1JCaGhtamw5c1pyWHA1cnlwSWdUMXYyZHVuazJybU1RYmp0QUtWd1Q5Y05VdmJ4N3JrRmF0NTBxVmVXdkh5QjU2K0FScEQ2aHRROHpNc2siLCJtYWMiOiIyZWExNDY0ZDhiMDljN2QyNDczNjJlNzY5OTZiODNhOGYxMWQzYzQ0NzdiNThhYmNjNWM5NTlmM2IwM2VhMjQ4IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 07:02:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905792444\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-896549763 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CIzKO9fBlSTpmHLFoM4m2LZDTPG6RfnweYwue2dV</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-896549763\", {\"maxDepth\":0})</script>\n"}}